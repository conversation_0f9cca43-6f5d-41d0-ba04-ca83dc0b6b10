# Customizing Firebase Authentication Email Templates

This document provides step-by-step instructions for customizing the Firebase Authentication email templates for BarcodeCafe.

## Setting Up Email Templates in Firebase Console

1. Go to the [Firebase Console](https://console.firebase.google.com/).
2. Select your project (`barcode-qr-menu`).
3. In the left sidebar, click on **Authentication**.
4. Go to the **Templates** tab.
5. You'll see different types of email templates:
   - **Email Verification**
   - **Password Reset**
   - **Email Address Change**
   - **Email Sign In with Email Link**

## Customizing Email Verification Template

1. Click on the **Email Verification** template.
2. Make these changes:
   - **Sender name**: `BarcodeCafe`
   - **Sender email**: Keep the default (`<EMAIL>`), or use a custom domain if you've set one up.
   - **Subject**: `Verify your email address for BarcodeCafe`
   - **Custom action URL**: `https://barcode-cafe.com/auth/action` (adjust to match your actual domain)

3. Under **Template Design**:
   - Update the logo: Click **Upload Logo** and select the BarcodeCafe logo.
   - Select the primary color: `#5DBDC0` (BarcodeCafe teal)
   - Update the footer text: `© 2025 BarcodeCafe. All rights reserved.`

4. Save your changes by clicking **Save** at the bottom of the page.

## Setting Up a Custom Domain for Email Verification

If you want to use a custom domain instead of the default `firebaseapp.com`:

1. While editing the template, click **Customize Domain**.
2. Enter your domain (e.g., `barcode-cafe.com`).
3. Follow the DNS verification steps to prove ownership:
   - Add the TXT records to your domain's DNS settings
   - Verify ownership
   - Wait for DNS propagation (can take up to 24-48 hours)

## Testing the Email Templates

After customizing your templates:

1. Create a test user account through your app.
2. Check the inbox for the email address you used.
3. Verify that the email follows your customized template.
4. Test the action link to ensure it properly redirects to your custom action handler.

## Important Notes

- If you're implementing a fully custom email sending solution using the API route created at `/api/auth/send-custom-verification`, you can skip the Firebase Console customization.
- Changes to email templates in the Firebase Console can take a few minutes to propagate.
- Always test all email flows after making changes.
- Consider testing on different email clients (Gmail, Outlook, etc.) to ensure proper rendering.

## Additional Resources

- [Firebase Authentication Documentation](https://firebase.google.com/docs/auth)
- [Customizing Email Action Handler](https://firebase.google.com/docs/auth/custom-email-handler) 