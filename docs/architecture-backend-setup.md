# BarcodeCafe QR Menu – Architecture & Backend Setup

This document outlines the architecture and backend approach for the BarcodeCafe QR Menu system.

---

## 🧱 Tech Stack Overview

| Layer         | Tool               | Purpose                               |
|---------------|--------------------|----------------------------------------|
| Frontend      | Next.js            | UI for customers & admin               |
| Hosting       | VPS                | Serves the Next.js app (static/SSR)    |
| API Layer     | Next.js API Routes | Handles all backend logic              |
| Backend (DB)  | Firebase Firestore | Stores menu, orders, coupons           |
| Auth          | Firebase Auth      | Admin authentication                   |
| Notifications | Email Service (e.g. SMTP, Resend) | Order/admin alerts via email      |
| Analytics     | Firebase Analytics | Tracks usage & customer behavior       |
| Media Storage | Firebase Storage   | Stores item images, logos, etc.        |

---

## 🚀 Backend API Routes (Next.js)

All Firestore interaction happens through `/api/*` routes.

Example endpoints:
- `POST /api/orders` → Place a new order
- `GET /api/menu` → Fetch menu by cafe ID
- `POST /api/coupons/validate` → Apply a coupon
- `GET /api/orders?status=pending` → Admin order list

This makes the app future-proof for:
- Mobile apps
- Role-based access control
- Switching databases (if needed)

---

## 🔐 Security Notes

- No direct Firestore access from frontend
- API routes verify data and securely access Firestore
- Firebase Admin SDK is used to verify ID tokens for admin access
- Firebase Security Rules act as a secondary guard layer

---

## 📈 Scalability Notes

- Firestore is highly scalable (1000s of concurrent users)
- VPS handles API load — scale vertically or move API to Vercel/Cloud Run if needed
- Static content is served fast via Next.js build or server-side render
- Designed to support more cafes/branches in the future with the same structure

---

## 📦 Deployment Tips

- Build Next.js for production:  
  `npm run build && npm start` (SSR) or `npm run export` (static)
- Serve on VPS using Nginx or PM2
- Firebase and email config stored securely via environment variables
- Analytics helps track order flow, coupon usage, and customer patterns

---

## 🔮 Future Roadmap

- Mobile app using the same API routes
- Firebase Cloud Functions for advanced logic
