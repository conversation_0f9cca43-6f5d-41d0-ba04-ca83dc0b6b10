# Admin User Setup

This document explains how to set up an admin user for the BarcodeCafe QR Menu application.

## Prerequisites

Before creating an admin user, make sure you have:

1. Firebase Admin SDK credentials:
   - Go to your Firebase project settings > Service accounts
   - Click "Generate new private key"
   - This will download a JSON file with your service account credentials

2. Set environment variables:
   ```
   # The contents of the service account JSON file
   FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account","project_id":"your-project-id", ... rest of the JSON ...}'
   
   # Admin User Credentials
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=SecurePassword123!
   ADMIN_NAME=Admin User
   ```

   You can set these in a `.env.local` file in the project root or as system environment variables.

## Creating an Admin User

The application supports a single admin account that has access to the admin dashboard. When this admin user logs in, they are automatically redirected to the admin dashboard.

### Option 1: Using the Admin Script

A setup script is provided to simplify admin user creation:

1. Make sure you have Node.js installed
2. Set the environment variables as described in the Prerequisites section
3. Run the script:
   ```bash
   # From the project root:
   npm run create-admin
   ```

### Option 2: Using the API Endpoint

You can create an admin user by sending a POST request to the `/api/auth/create-admin` endpoint:

```bash
curl -X POST http://localhost:3000/api/auth/create-admin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "displayName": "Admin User"
  }'
```

Note: The API endpoint requires the application server to be running.

### Option 3: Manual Setup (Recommended)

If the automated methods above don't work due to permission issues with the Firebase Admin SDK, you can manually set up an admin account:

1. First, start the application:
   ```bash
   npm run dev
   ```

2. Navigate to http://localhost:3000/signup in your browser

3. Register a new user account with:
   - Email: <EMAIL>
   - Password: Admin123! (or your preferred password)
   - Complete the registration process and verify your email if required

4. Using the Firebase Console:
   - Go to your Firebase project's Firestore Database
   - Create a new collection called "admins" if it doesn't exist
   - Add a new document with:
     - Document ID: [The UID of the user you just created]
     - Fields:
       - uid: [The UID of the user] (string)
       - email: <EMAIL> (string)
       - displayName: Admin User (string)
       - createdAt: [Current Date/Time] (timestamp)
       - updatedAt: [Current Date/Time] (timestamp)

5. After adding this document to the "admins" collection, sign out and sign back in.
   - You will now be automatically redirected to the admin dashboard when you log in.

## Admin User Authentication

Once created, the admin user can log in through the normal login page. The system will automatically:

1. Check if the user is an admin by looking up the user's ID in the `admins` collection in Firestore
2. Redirect the admin user to `/admin/dashboard` instead of the customer dashboard 

## Admin Dashboard Access

The admin dashboard is accessible at:
- `/admin/dashboard` 

This dashboard contains all the administrative functions for managing the cafe.

## Security Considerations

- The admin user creation endpoint should be properly secured in a production environment
- Consider implementing additional security measures such as:
  - Rate limiting
  - IP restrictions
  - Further authentication for the admin API endpoints
- Store your Firebase service account key securely and don't expose it in client-side code 