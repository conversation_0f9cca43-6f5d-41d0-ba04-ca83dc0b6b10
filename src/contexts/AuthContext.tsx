'use client';

import { 
  createContext, 
  useContext, 
  useState, 
  useEffect, 
  ReactNode 
} from 'react';
import { onAuthStateChanged, User } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import { 
  loginWithEmailAndPassword, 
  loginWithGoogle as loginWithGoogleUtil, 
  logout as logoutUtil,
  registerWithEmailAndPassword,
  setSessionCookie,
  sendVerificationEmail,
  resetPassword as resetPasswordUtil
} from '@/lib/firebase/auth-utils';
import { toast } from '@/hooks/use-toast';
import { isUserAdmin } from '@/lib/firebase/firestore';

// Authentication context type
interface AuthContextType {
  user: User | null;
  loading: boolean;
  isEmailVerified: boolean;
  isAdmin: boolean;
  login: (email: string, password: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  sendVerification: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Auth Provider Component
 * Provides authentication state and methods to the app
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEmailVerified, setIsEmailVerified] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      console.log("AuthContext: Auth state changed - user:", user ? user.uid : "no user");
      setUser(user);
      setLoading(false);
      
      if (user) {
        // Set session cookie when user logs in
        await setSessionCookie(user);
        
        // Check email verification status
        setIsEmailVerified(user.emailVerified);
        
        // Reload user to get latest email verification status
        await user.reload();
        setIsEmailVerified(user.emailVerified);
        
        // Check if user is an admin
        try {
          console.log("AuthContext: Checking admin status for user:", user.uid);
          const adminStatus = await isUserAdmin(user.uid);
          console.log("AuthContext: Admin status result:", adminStatus);
          
          // Force a token refresh if admin status changed
          if (adminStatus !== isAdmin) {
            console.log("AuthContext: Admin status changed, refreshing token");
            await user.getIdToken(true);
          }
          
          setIsAdmin(adminStatus);
          
          // Log final state
          console.log("AuthContext: Final admin state set to:", adminStatus);
        } catch (error) {
          console.error('AuthContext: Error checking admin status:', error);
          setIsAdmin(false);
        }
      } else {
        setIsEmailVerified(false);
        setIsAdmin(false);
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  /**
   * Login with email/password
   */
  const login = async (email: string, password: string) => {
    try {
      const userCredential = await loginWithEmailAndPassword(email, password);
      
      // Check if email is verified
      if (!userCredential.user.emailVerified) {
        // Send a verification email if it's not verified
        await sendVerificationEmail(userCredential.user);
        
        toast({
          variant: "destructive",
          title: "Email not verified",
          description: "We've sent a verification email. Please check your inbox and verify your email before logging in.",
        });
        
        // Sign out the user immediately
        await logoutUtil();
        throw new Error('Please verify your email before logging in');
      }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  /**
   * Login with Google
   */
  const loginWithGoogle = async () => {
    try {
      await loginWithGoogleUtil();
      // Google accounts are auto-verified, no need to check verification
    } catch (error) {
      console.error('Google login failed:', error);
      throw error;
    }
  };

  /**
   * Register with email/password
   */
  const register = async (email: string, password: string) => {
    try {
      const userCredential = await registerWithEmailAndPassword(email, password);
      
      let emailSent = false;
      
      // Try the custom email template API first
      try {
        // Call the custom API to send a styled verification email
        const response = await fetch('/api/auth/send-custom-verification', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            uid: userCredential.user.uid,
            email: userCredential.user.email,
            displayName: userCredential.user.displayName || 'User',
          }),
        });
        
        const data = await response.json();
        
        if (data.success) {
          emailSent = true;
        }
      } catch (error) {
        console.error('Failed to send custom verification email:', error);
      }
      
      // If custom email failed, fallback to Firebase's default verification email
      if (!emailSent) {
        await sendVerificationEmail(userCredential.user);
      }
      
      // Create user profile in Firestore
      try {
        // Import here to avoid circular dependencies
        const { createUserProfile } = await import('@/lib/firebase/firestore');
        
        await createUserProfile(userCredential.user.uid, {
          email: userCredential.user.email || '',
          displayName: userCredential.user.displayName || '',
          photoURL: userCredential.user.photoURL || '',
        });
      } catch (profileError) {
        console.error('Error creating user profile:', profileError);
        // Don't fail registration if profile creation fails
      }
      
      // Log out the user after registration - they need to verify email
      await logoutUtil();
      
      toast({
        variant: "success",
        title: "Registration successful",
        description: "Please check your email to verify your account before logging in.",
      });
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  };

  /**
   * Logout
   */
  const logout = async () => {
    try {
      await logoutUtil();
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  };

  /**
   * Send verification email
   */
  const sendVerification = async () => {
    if (!user) {
      throw new Error('No user is signed in');
    }
    
    try {
      await sendVerificationEmail(user);
      
      toast({
        variant: "success",
        title: "Verification email sent",
        description: "Please check your inbox and verify your email address.",
      });
    } catch (error) {
      console.error('Failed to send verification email:', error);
      
      toast({
        variant: "destructive",
        title: "Failed to send verification email",
        description: "Please try again later.",
      });
      
      throw error;
    }
  };

  /**
   * Reset password
   */
  const resetPassword = async (email: string) => {
    try {
      await resetPasswordUtil(email);
    } catch (error) {
      console.error('Password reset failed:', error);
      throw error;
    }
  };

  // Context value
  const value = {
    user,
    loading,
    isEmailVerified,
    isAdmin,
    login,
    loginWithGoogle,
    register,
    logout,
    sendVerification,
    resetPassword,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
} 