"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { usePathname } from 'next/navigation';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  isDark: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = 'barcode-cafe-theme';

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Always start with 'light' as the default for server-side rendering
  const [theme, setThemeState] = useState<Theme>('light');
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname();
  
  // Check if current page is the menu page
  const isMenuPage = pathname === '/menu';

  // Initialize client-side state after mounting
  useEffect(() => {
    setMounted(true);
    
    // Check localStorage after component mounts (client-side only)
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
    if (savedTheme === 'dark') {
      setThemeState('dark');
    } else if (!savedTheme) {
      // Check system preference if no saved preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      if (prefersDark) {
        setThemeState('dark');
      }
    }
  }, []);

  // Effect to update the document class and localStorage when theme changes
  useEffect(() => {
    if (!mounted) return;
    
    // Save to localStorage whenever theme changes
    localStorage.setItem(THEME_STORAGE_KEY, theme);
    
    // Apply theme to document, but not on /menu page
    if (theme === 'dark' && !isMenuPage) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [theme, mounted, isMenuPage, pathname]);

  // Function to set the theme
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  // Function to toggle between light and dark
  const toggleTheme = () => {
    setThemeState(prevTheme => prevTheme === 'light' ? 'dark' : 'light');
  };
  
  // Computed property for current theme state
  // On the menu page, always return false to indicate light mode
  const isDark = isMenuPage ? false : theme === 'dark';

  // Prevent flash of incorrect theme
  if (!mounted) {
    return null;
  }

  return (
    <ThemeContext.Provider value={{ theme, setTheme, toggleTheme, isDark }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}; 