"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { 
  initializeApp, 
  getApps, 
  getApp 
} from "firebase/app";
import { 
  getAuth, 
  applyActionCode, 
  verifyPasswordResetCode, 
  confirmPasswordReset,
  checkActionCode
} from "firebase/auth";
import Logo from "@/components/ui/Logo";
import Link from "next/link";

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
};

// Initialize Firebase only once
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
const auth = getAuth(app);

enum ActionMode {
  VerifyEmail = "verifyEmail",
  ResetPassword = "resetPassword",
  RecoverEmail = "recoverEmail",
  Unknown = "unknown"
}

enum ActionStatus {
  Processing = "processing",
  Success = "success",
  Error = "error"
}

export default function AuthActionPage() {
  const searchParams = useSearchParams();
  const [mode, setMode] = useState<ActionMode>(ActionMode.Unknown);
  const [status, setStatus] = useState<ActionStatus>(ActionStatus.Processing);
  const [message, setMessage] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [newPassword, setNewPassword] = useState<string>("");
  const [confirmPassword, setConfirmPassword] = useState<string>("");
  const [email, setEmail] = useState<string>("");

  useEffect(() => {
    const actionMode = searchParams.get("mode");
    const actionCode = searchParams.get("oobCode");

    if (!actionCode) {
      setStatus(ActionStatus.Error);
      setError("Invalid action code. The link may have expired or been used already.");
      return;
    }

    // Set the action mode
    if (actionMode === "verifyEmail") {
      setMode(ActionMode.VerifyEmail);
      handleVerifyEmail(actionCode);
    } else if (actionMode === "resetPassword") {
      setMode(ActionMode.ResetPassword);
      handleResetPassword(actionCode);
    } else if (actionMode === "recoverEmail") {
      setMode(ActionMode.RecoverEmail);
      handleRecoverEmail(actionCode);
    } else {
      setStatus(ActionStatus.Error);
      setError("Invalid action mode.");
    }
  }, [searchParams]);

  const handleVerifyEmail = async (actionCode: string) => {
    try {
      console.log("Verifying email with action code...");
      // First check the action code before applying it
      try {
        await checkActionCode(auth, actionCode);
      } catch (checkError) {
        console.error("Invalid action code during check:", checkError);
        throw checkError;
      }
      
      await applyActionCode(auth, actionCode);
      setStatus(ActionStatus.Success);
      setMessage("Your email has been verified! You can now sign in to your BarcodeCafe account.");
    } catch (error: unknown) {
      console.error("Error verifying email:", error);
      
      let errorMessage = "Could not verify your email. The link may have expired or been used already.";
      if (error && typeof error === 'object' && 'code' in error) {
        const firebaseError = error as { code: string };
        if (firebaseError.code === "auth/invalid-action-code") {
          errorMessage = "Invalid verification link. The link may have expired, been malformed, or already used.";
        } else if (firebaseError.code === "auth/expired-action-code") {
          errorMessage = "This verification link has expired. Please request a new verification email.";
        } else if (firebaseError.code === "auth/user-disabled") {
          errorMessage = "This user account has been disabled. Please contact support.";
        } else if (firebaseError.code === "auth/user-not-found") {
          errorMessage = "User account not found. Please register again.";
        }
      }
      
      setStatus(ActionStatus.Error);
      setError(errorMessage);
    }
  };

  const handleResetPassword = async (actionCode: string) => {
    try {
      const email = await verifyPasswordResetCode(auth, actionCode);
      setEmail(email);
      setStatus(ActionStatus.Success);
    } catch (error) {
      console.error("Error verifying reset password code:", error);
      setStatus(ActionStatus.Error);
      setError("Invalid password reset link. The link may have expired or been used already.");
    }
  };

  const handleSubmitNewPassword = async (actionCode: string) => {
    if (newPassword.length < 6) {
      setError("Password must be at least 6 characters.");
      return;
    }
    
    if (newPassword !== confirmPassword) {
      setError("Passwords don't match.");
      return;
    }

    try {
      await confirmPasswordReset(auth, actionCode, newPassword);
      setMessage("Your password has been reset successfully. You can now sign in with your new password.");
      setStatus(ActionStatus.Success);
    } catch (error) {
      console.error("Error confirming password reset:", error);
      setStatus(ActionStatus.Error);
      setError("Failed to reset your password. Please try again.");
    }
  };

  const handleRecoverEmail = async (actionCode: string) => {
    try {
      const info = await checkActionCode(auth, actionCode);
      const email = info.data.email || "";
      
      await applyActionCode(auth, actionCode);
      setStatus(ActionStatus.Success);
      setMessage(`Your email has been restored to ${email}. If you did not request this change, please secure your account immediately.`);
    } catch (error) {
      console.error("Error recovering email:", error);
      setStatus(ActionStatus.Error);
      setError("Could not recover your email. The link may have expired or been used already.");
    }
  };

  return (
    <div className="min-h-screen bg-[#F9FDFC] dark:bg-[#171c23] flex flex-col">
      {/* Header */}
      <header className="py-6 px-8 border-b border-gray-200 dark:border-gray-800">
        <div className="max-w-7xl mx-auto flex items-center">
          <Logo linkTo="/" />
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-white dark:bg-[#1d2127] rounded-xl shadow-lg p-8">
          {status === ActionStatus.Processing && (
            <div className="text-center py-8">
              <div className="animate-spin w-12 h-12 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">Processing your request...</p>
            </div>
          )}

          {status === ActionStatus.Error && (
            <div className="text-center py-6">
              <div className="bg-red-100 dark:bg-red-900/20 w-20 h-20 flex items-center justify-center rounded-full mx-auto mb-4">
                <i className="fa-solid fa-exclamation-circle text-red-600 dark:text-red-500 text-3xl"></i>
              </div>
              <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Error</h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">{error}</p>
              <Link 
                href="/signin" 
                className="inline-block bg-primary text-white py-2 px-6 rounded-lg hover:bg-primary/90 transition-colors"
              >
                Back to Sign In
              </Link>
            </div>
          )}

          {status === ActionStatus.Success && mode === ActionMode.VerifyEmail && (
            <div className="text-center py-6">
              <div className="bg-green-100 dark:bg-green-900/20 w-20 h-20 flex items-center justify-center rounded-full mx-auto mb-4">
                <i className="fa-solid fa-check-circle text-green-600 dark:text-green-500 text-3xl"></i>
              </div>
              <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Email Verified</h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">{message}</p>
              <Link 
                href="/signin" 
                className="inline-block bg-primary text-white py-2 px-6 rounded-lg hover:bg-primary/90 transition-colors"
              >
                Sign In
              </Link>
            </div>
          )}

          {mode === ActionMode.ResetPassword && status === ActionStatus.Success && !message && (
            <div className="py-6">
              <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 text-center">Reset Your Password</h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6 text-center">
                Please enter a new password for <span className="font-medium">{email}</span>
              </p>
              
              {error && (
                <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg text-sm">
                  {error}
                </div>
              )}
              
              <form className="space-y-4">
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    New Password
                  </label>
                  <input
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Enter new password"
                  />
                </div>
                
                <div className="space-y-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Confirm Password
                  </label>
                  <input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Confirm new password"
                  />
                </div>
                
                <button
                  type="button"
                  onClick={() => handleSubmitNewPassword(searchParams.get("oobCode") || "")}
                  className="w-full bg-primary text-white py-3 rounded-lg hover:bg-primary/90 transition-colors font-medium"
                >
                  Reset Password
                </button>
              </form>
            </div>
          )}

          {mode === ActionMode.ResetPassword && status === ActionStatus.Success && message && (
            <div className="text-center py-6">
              <div className="bg-green-100 dark:bg-green-900/20 w-20 h-20 flex items-center justify-center rounded-full mx-auto mb-4">
                <i className="fa-solid fa-check-circle text-green-600 dark:text-green-500 text-3xl"></i>
              </div>
              <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Password Reset</h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">{message}</p>
              <Link 
                href="/signin" 
                className="inline-block bg-primary text-white py-2 px-6 rounded-lg hover:bg-primary/90 transition-colors"
              >
                Sign In
              </Link>
            </div>
          )}

          {status === ActionStatus.Success && mode === ActionMode.RecoverEmail && (
            <div className="text-center py-6">
              <div className="bg-green-100 dark:bg-green-900/20 w-20 h-20 flex items-center justify-center rounded-full mx-auto mb-4">
                <i className="fa-solid fa-check-circle text-green-600 dark:text-green-500 text-3xl"></i>
              </div>
              <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-4">Email Recovered</h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">{message}</p>
              <Link 
                href="/signin" 
                className="inline-block bg-primary text-white py-2 px-6 rounded-lg hover:bg-primary/90 transition-colors"
              >
                Sign In
              </Link>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="py-6 text-center text-gray-500 dark:text-gray-400 text-sm border-t border-gray-200 dark:border-gray-800">
        <p>© {new Date().getFullYear()} BarcodeCafe. All rights reserved.</p>
      </footer>
    </div>
  );
} 