@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;500;600;700;800;900&display=swap');

/* Arabic font declaration */
@font-face {
  font-family: 'Noto Kufi Arabic';
  src: url('../assets/fonts/noto-kufi-arabic.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* Font application for Arabic - make sure it's compatible with Tailwind, but exclude icons */
html[lang="ar"] {
  font-family: 'Noto Kufi Arabic', sans-serif !important;
}

html[lang="ar"] *:not(i):not(.icon-exempt) {
  font-family: 'Noto Kufi Arabic', sans-serif !important;
}

/* Explicitly set Font Awesome icons to use their own font */
i.fa-brands, 
i.fa-solid, 
i.fa-regular,
i.fa-light,
i.fa-duotone {
  font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Brands', sans-serif !important;
}

/* Make sure icons always display properly regardless of text direction */
[dir="rtl"] i.fa-brands,
[dir="rtl"] i.fa-solid,
[dir="rtl"] i.fa-regular,
[dir="rtl"] i.fa-light,
[dir="rtl"] i.fa-duotone {
  display: inline-block !important;
  transform: none !important;
  font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Brands', sans-serif !important;
}

/* Tailwind-friendly RTL utilities that can be used with Tailwind classes */
.rtl\:text-right[dir="rtl"] {
  text-align: right;
}

.rtl\:text-left[dir="rtl"] {
  text-align: left;
}

/* Layout fixes for RTL mode - prevent unwanted flipping */
[dir="rtl"] .no-flip {
  flex-direction: row !important;
  display: flex;
}

/* Prevent direction changes in grid layouts */
[dir="rtl"] .grid {
  direction: rtl;
}

/* Social icons container - maintain order */
[dir="rtl"] .social-container {
  display: grid;
  direction: ltr;
}

/* Flip appropriate flex containers */
[dir="rtl"] .flip-in-rtl {
  flex-direction: row-reverse;
}

/* Proper spacing in RTL mode */
[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Form elements in RTL */
[dir="rtl"] input, 
[dir="rtl"] textarea, 
[dir="rtl"] select {
  text-align: right;
}

/* Special case for label and input containers */
[dir="rtl"] .input-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* Center-aligned elements remain centered */
.center-align-rtl {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

/* RTL text handling */
[dir="rtl"] .rtl-text {
  display: inline-block;
  text-align: right;
  direction: rtl;
  unicode-bidi: embed;
}

/* Special case for terms checkbox to align correctly */
[dir="rtl"] .terms-container {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
}

[dir="rtl"] .terms-container input[type="checkbox"] {
  margin-left: 0;
  margin-right: 0;
}

/* Header elements in RTL */
[dir="rtl"] .header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

/* Logo container in RTL */
[dir="rtl"] .logo-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
  
  /* Custom Barcode Cafe Colors */
  --color-base: #ffffff;
  --color-base-50: #f9fafb;
  --color-base-100: #f3f4f6;
  --color-base-200: #e5e7eb;
  --color-base-300: #d1d5db;
  --color-base-400: #9ca3af;
  --color-base-500: #6b7280;
  --color-base-600: #4b5563;  
  --color-base-700: #374151;
  --color-base-800: #1f2937;  
  --color-base-900: #111827;
  --color-base-content: #1f2937;

  /* Primary colors */
  --color-primary: #3b82f6;
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-focus: #2563eb;
  --color-primary-content: #ffffff;

  /* Cafe theme colors */
  --cafe-green-dark: #00441B;
  --cafe-green-medium: #238B45;
  --cafe-green-light: #41AB5D;
  --cafe-green-lighter: #74C476;
  --cafe-green-lightest: #A1D99B;
  --cafe-bg-light: #F7FCF5;
  --cafe-accent: #E5F5E0;
  --cafe-accent-hover: #C7E9C0;
  
  /* Button and UI colors */
  --button-primary: #83EAED;
  --button-primary-hover: #6ED3D6;
  --button-text: #ffffff;
}

.dark {
  --background: #171c23;
  --foreground: #f3f4f6;
  --card: #1d2127;
  --card-foreground: #f3f4f6;
  --popover: #1d2127;
  --popover-foreground: #f3f4f6;
  --primary: #f3f4f6;
  --primary-foreground: #1d2127;
  --secondary: #242832;
  --secondary-foreground: #f3f4f6;
  --muted: #242832;
  --muted-foreground: #9ca3af;
  --accent: #242832;
  --accent-foreground: #f3f4f6;
  --destructive: #ef4444;
  --border: rgba(243, 244, 246, 0.1);
  --input: rgba(243, 244, 246, 0.15);
  --ring: #4b5563;
  
  /* Dark mode overrides for Barcode Cafe */
  --color-base: #171c23;
  --color-base-50: #1d2127;
  --color-base-100: #242832;
  --color-base-200: #374151;
  --color-base-300: #4b5563;
  --color-base-400: #6b7280;
  --color-base-500: #9ca3af;
  --color-base-600: #d1d5db;
  --color-base-700: #e5e7eb;
  --color-base-800: #f3f4f6;
  --color-base-900: #f9fafb;
  --color-base-content: #f9fafb;
  
  /* Cafe theme colors - darkened */
  --cafe-green-dark: #00331A;
  --cafe-green-medium: #1C6B35;
  --cafe-green-light: #35884B;
  --cafe-green-lighter: #5DA45F;
  --cafe-green-lightest: #81AD7D;
  --cafe-bg-light: #1d2127;
  --cafe-accent: #1C3A1A;
  --cafe-accent-hover: #2A552A;
  
  /* Button and UI colors */
  --button-primary: #5DBDC0;
  --button-primary-hover: #4A9EA0;
  --button-text: #171c23;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    font-family: "Inter", sans-serif;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom utility classes */
.bg-cafe-green-dark {
  background-color: var(--cafe-green-dark);
}

.bg-cafe-green-medium {
  background-color: var(--cafe-green-medium);
}

.bg-cafe-green-light {
  background-color: var(--cafe-green-light);
}

.bg-cafe-light {
  background-color: var(--cafe-bg-light);
}

.text-cafe-green-dark {
  color: var(--cafe-green-dark);
}

.text-cafe-green-medium {
  color: var(--cafe-green-medium);
}

.text-cafe-green-light {
  color: var(--cafe-green-light);
}

/* Hide scrollbar for Chrome, Safari and Opera */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
* {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Sign Up Page Styles */
#signup-container input[type="checkbox"] {
  accent-color: #41AB5D;
}

#signup-divider span {
  background-color: white;
}

@media (max-width: 1023px) {
  #signup-card {
    flex-direction: column;
  }
}

/* Sign In Page Styles */
#signin-container input[type="checkbox"] {
  accent-color: #41AB5D;
}

#signin-divider span {
  background-color: white;
}

@media (max-width: 1023px) {
  #signin-card {
    flex-direction: column;
  }
}

/* Center alignment for buttons/elements that should be same in both versions */
.center-align {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

/* Add these rules to fix RTL icon issues */

/* Ensure social icons container always maintains LTR for consistent display */
.social-icon-grid {
  direction: ltr !important;
}

/* Force LTR on the logo container to prevent mirroring */
.logo-container {
  direction: ltr !important;
  display: flex !important;
  align-items: center !important;
}

/* Specifically target language switcher to maintain consistent appearance */
.lang-switcher {
  direction: ltr !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out !important;
  border: 1px solid transparent !important;
}

.lang-switcher:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.dark .lang-switcher {
  border-color: rgba(55, 65, 81, 0.5) !important;
}

/* Ensure language dropdown has proper styling in all contexts */
[dir="rtl"] .lang-dropdown-menu {
  left: 0 !important;
  right: auto !important;
  direction: ltr !important;
  text-align: left !important;
}

[dir="ltr"] .lang-dropdown-menu {
  right: 0 !important;
  left: auto !important;
}

.dark .lang-dropdown-menu {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.15) !important;
}
