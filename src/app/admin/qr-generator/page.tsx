"use client";

import { useLocale } from "@/contexts/LocaleContext";

export default function QRGeneratorPage() {
  const { t, isClient } = useLocale();
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          {isClient ? t('admin.qrGeneratorMenu') : 'QR Generator'}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {isClient ? t('admin.qrGeneratorDescription') : 'Generate QR codes for your cafe menu and tables'}
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* QR Code Generator Form */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
            <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">
              {isClient ? t('admin.qrGenerator.generateNew') : 'Generate New QR Code'}
            </h3>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isClient ? t('admin.qrGenerator.codeType') : 'QR Code Type'}
                  </label>
                  <select className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]">
                    <option>{isClient ? t('admin.qrGenerator.menuItem') : 'Menu Item'}</option>
                    <option>{isClient ? t('admin.qrGenerator.tableNumber') : 'Table Number'}</option>
                    <option>{isClient ? t('admin.qrGenerator.specialOffer') : 'Special Offer'}</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isClient ? t('admin.qrGenerator.size') : 'Size (px)'}
                  </label>
                  <input 
                    type="number" 
                    defaultValue="300" 
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isClient ? t('admin.qrGenerator.contentUrl') : 'Content/URL'}
                </label>
                <input 
                  type="text" 
                  placeholder={isClient ? t('admin.qrGenerator.urlPlaceholder') : 'Enter URL or content for QR code'} 
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-[#242832] text-gray-700 dark:text-gray-200 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isClient ? t('admin.qrGenerator.foregroundColor') : 'Foreground Color'}
                  </label>
                  <input 
                    type="color" 
                    defaultValue="#56999B" 
                    className="w-full h-10 rounded-lg"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isClient ? t('admin.qrGenerator.backgroundColor') : 'Background Color'}
                  </label>
                  <input 
                    type="color" 
                    defaultValue="#FFFFFF" 
                    className="w-full h-10 rounded-lg"
                  />
                </div>
              </div>

              <button className="w-full bg-[#74C8CA] hover:bg-[#56999B] dark:bg-[#5DBDC0] dark:hover:bg-[#56999B] text-white font-medium py-3 rounded-lg transition-colors">
                {isClient ? t('admin.qrGenerator.generateButton') : 'Generate QR Code'}
              </button>
            </div>
          </div>
        </div>

        {/* QR Code Preview */}
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
          <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">
            {isClient ? t('admin.qrGenerator.preview') : 'Preview'}
          </h3>
          <div className="flex flex-col items-center space-y-6">
            <div className="w-48 h-48 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
              <i className="fa-solid fa-qrcode text-6xl text-[#74C8CA] dark:text-[#5DBDC0]"></i>
            </div>
            <div className="flex space-x-3">
              <button className="px-4 py-2 bg-[#A8FDFF] dark:bg-[#5DBDC0]/30 text-[#56999B] dark:text-[#5DBDC0] rounded-lg hover:bg-[#83EAED] dark:hover:bg-[#5DBDC0]/40 transition-colors">
                <i className="fa-solid fa-download mr-2"></i>
                {isClient ? t('admin.qrGenerator.download') : 'Download'}
              </button>
              <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                <i className="fa-solid fa-print mr-2"></i>
                {isClient ? t('admin.qrGenerator.print') : 'Print'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Recent QR Codes */}
      <div className="mt-8">
        <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-6">
          {isClient ? t('admin.qrGenerator.recentCodes') : 'Recently Generated QR Codes'}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* QR Code Card 1 */}
          <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-4">
            <div className="w-full aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center mb-4">
              <i className="fa-solid fa-qrcode text-4xl text-[#74C8CA] dark:text-[#5DBDC0]"></i>
            </div>
            <div className="text-sm">
              <p className="font-medium text-gray-800 dark:text-white">{isClient ? t('admin.qrGenerator.table') : 'Table'} #12</p>
              <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">{isClient ? t('admin.qrGenerator.generatedOn') : 'Generated on'} Jan 15, 2025</p>
            </div>
          </div>

          {/* QR Code Card 2 */}
          <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-4">
            <div className="w-full aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center mb-4">
              <i className="fa-solid fa-qrcode text-4xl text-[#74C8CA] dark:text-[#5DBDC0]"></i>
            </div>
            <div className="text-sm">
              <p className="font-medium text-gray-800 dark:text-white">{isClient ? t('admin.qrGenerator.specialMenu') : 'Special Menu'}</p>
              <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">{isClient ? t('admin.qrGenerator.generatedOn') : 'Generated on'} Jan 14, 2025</p>
            </div>
          </div>

          {/* QR Code Card 3 */}
          <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-4">
            <div className="w-full aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center mb-4">
              <i className="fa-solid fa-qrcode text-4xl text-[#74C8CA] dark:text-[#5DBDC0]"></i>
            </div>
            <div className="text-sm">
              <p className="font-medium text-gray-800 dark:text-white">{isClient ? t('admin.qrGenerator.breakfastMenu') : 'Breakfast Menu'}</p>
              <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">{isClient ? t('admin.qrGenerator.generatedOn') : 'Generated on'} Jan 13, 2025</p>
            </div>
          </div>

          {/* QR Code Card 4 */}
          <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-4">
            <div className="w-full aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center mb-4">
              <i className="fa-solid fa-qrcode text-4xl text-[#74C8CA] dark:text-[#5DBDC0]"></i>
            </div>
            <div className="text-sm">
              <p className="font-medium text-gray-800 dark:text-white">{isClient ? t('admin.qrGenerator.weekendOffers') : 'Weekend Offers'}</p>
              <p className="text-gray-500 dark:text-gray-400 text-xs mt-1">{isClient ? t('admin.qrGenerator.generatedOn') : 'Generated on'} Jan 12, 2025</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 