"use client";

import { useEffect, useState } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { getUserMenuItemCount, getActiveCategoryCount } from "@/lib/firebase/firestore";
import { useAuth } from "@/contexts/AuthContext";
import Link from "next/link";

export default function AdminDashboardPage() {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  
  const [menuItemCount, setMenuItemCount] = useState<number | null>(null);
  const [categoryCount, setCategoryCount] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    if (user?.uid) {
      fetchDashboardData();
    }
  }, [user?.uid]);
  
  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch counts
      const itemCount = await getUserMenuItemCount(user!.uid);
      setMenuItemCount(itemCount);
      
      const activeCats = await getActiveCategoryCount(user!.uid);
      setCategoryCount(activeCats);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Display skeleton loader when counts are loading
  const renderCount = (count: number | null) => {
    if (isLoading) {
      return (
        <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div>
      );
    }
    return count !== null ? count : '0';
  };
  
  return (
    <div className="space-y-8">
      {/* Dashboard Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          {isClient ? t('admin.dashboard') : 'Dashboard Overview'}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {isClient ? t('admin.welcomeMessage') : 'Welcome back! Here&apos;s what&apos;s happening with your cafe today.'}
        </p>
      </div>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-[#1d2127] p-6 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{isClient ? t('admin.stats.today') : "Today's Sales"}</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">$1,259</h3>
            </div>
            <i className="fa-solid fa-dollar-sign text-[#56999B] dark:text-[#5DBDC0] text-2xl"></i>
          </div>
        </div>

        <div className="bg-white dark:bg-[#1d2127] p-6 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{isClient ? t('admin.totalOrders') : 'Total Orders'}</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">48</h3>
            </div>
            <i className="fa-solid fa-shopping-bag text-[#56999B] dark:text-[#5DBDC0] text-2xl"></i>
          </div>
        </div>

        <div className="bg-white dark:bg-[#1d2127] p-6 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{isClient ? t('admin.activeMenuItems') : 'Active Menu Items'}</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">{renderCount(menuItemCount)}</h3>
            </div>
            <i className="fa-solid fa-utensils text-[#56999B] dark:text-[#5DBDC0] text-2xl"></i>
          </div>
        </div>

        <div className="bg-white dark:bg-[#1d2127] p-6 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{isClient ? t('admin.categories') : 'Categories'}</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">{renderCount(categoryCount)}</h3>
            </div>
            <i className="fa-solid fa-th-large text-[#56999B] dark:text-[#5DBDC0] text-2xl"></i>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6 mb-8">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-4">{isClient ? t('admin.quickActions') : 'Quick Actions'}</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Link 
            href="/admin/menu-items/add"
            className="flex flex-col items-center justify-center p-4 border border-[#83EAED] dark:border-[#5DBDC0] rounded-lg hover:bg-[#A8FDFF]/20 dark:hover:bg-[#5DBDC0]/20 transition-colors"
          >
            <i className="fa-solid fa-plus-circle text-[#56999B] dark:text-[#5DBDC0] text-2xl mb-2"></i>
            <span className="text-sm text-gray-600 dark:text-gray-300">{isClient ? t('admin.addItemMenu') : 'Add Menu Item'}</span>
          </Link>
          <Link 
            href="/admin/categories"
            className="flex flex-col items-center justify-center p-4 border border-[#83EAED] dark:border-[#5DBDC0] rounded-lg hover:bg-[#A8FDFF]/20 dark:hover:bg-[#5DBDC0]/20 transition-colors"
          >
            <i className="fa-solid fa-th-large text-[#56999B] dark:text-[#5DBDC0] text-2xl mb-2"></i>
            <span className="text-sm text-gray-600 dark:text-gray-300">{isClient ? t('admin.manageCategories') : 'Manage Categories'}</span>
          </Link>
          <Link 
            href="/admin/qr-generator"
            className="flex flex-col items-center justify-center p-4 border border-[#83EAED] dark:border-[#5DBDC0] rounded-lg hover:bg-[#A8FDFF]/20 dark:hover:bg-[#5DBDC0]/20 transition-colors"
          >
            <i className="fa-solid fa-qrcode text-[#56999B] dark:text-[#5DBDC0] text-2xl mb-2"></i>
            <span className="text-sm text-gray-600 dark:text-gray-300">{isClient ? t('admin.generateQR') : 'Generate QR'}</span>
          </Link>
          <Link 
            href="/admin/menu-items"
            className="flex flex-col items-center justify-center p-4 border border-[#83EAED] dark:border-[#5DBDC0] rounded-lg hover:bg-[#A8FDFF]/20 dark:hover:bg-[#5DBDC0]/20 transition-colors"
          >
            <i className="fa-solid fa-utensils text-[#56999B] dark:text-[#5DBDC0] text-2xl mb-2"></i>
            <span className="text-sm text-gray-600 dark:text-gray-300">{isClient ? t('admin.viewMenuItems') : 'View Menu Items'}</span>
          </Link>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-800 dark:text-white">{isClient ? t('admin.recentOrders') : 'Recent Orders'}</h2>
          <button className="text-[#56999B] dark:text-[#5DBDC0] hover:text-[#74C8CA]">{isClient ? t('admin.viewAll') : 'View All'}</button>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-sm text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                <th className="pb-3">{isClient ? t('admin.orderId') : 'Order ID'}</th>
                <th className="pb-3">{isClient ? t('admin.customer') : 'Customer'}</th>
                <th className="pb-3">{isClient ? t('admin.items') : 'Items'}</th>
                <th className="pb-3">{isClient ? t('admin.total') : 'Total'}</th>
                <th className="pb-3">{isClient ? t('admin.statusColumn') : 'Status'}</th>
                <th className="pb-3">{isClient ? t('admin.actionColumn') : 'Action'}</th>
              </tr>
            </thead>
            <tbody className="text-sm">
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <td className="py-3 text-gray-800 dark:text-gray-200">#ORD-001</td>
                <td className="py-3 text-gray-800 dark:text-gray-200">John Doe</td>
                <td className="py-3 text-gray-800 dark:text-gray-200">2 {isClient ? t('admin.itemsCount') : 'items'}</td>
                <td className="py-3 text-gray-800 dark:text-gray-200">$24.99</td>
                <td className="py-3"><span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-full text-xs">{isClient ? t('admin.orderStatusTypes.completed') : 'Completed'}</span></td>
                <td className="py-3"><button className="text-[#56999B] dark:text-[#5DBDC0]">{isClient ? t('admin.view') : 'View'}</button></td>
              </tr>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <td className="py-3 text-gray-800 dark:text-gray-200">#ORD-002</td>
                <td className="py-3 text-gray-800 dark:text-gray-200">Jane Smith</td>
                <td className="py-3 text-gray-800 dark:text-gray-200">1 {isClient ? t('admin.itemCount') : 'item'}</td>
                <td className="py-3 text-gray-800 dark:text-gray-200">$18.50</td>
                <td className="py-3"><span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 rounded-full text-xs">{isClient ? t('admin.orderStatusTypes.processing') : 'Processing'}</span></td>
                <td className="py-3"><button className="text-[#56999B] dark:text-[#5DBDC0]">{isClient ? t('admin.view') : 'View'}</button></td>
              </tr>
              <tr>
                <td className="py-3 text-gray-800 dark:text-gray-200">#ORD-003</td>
                <td className="py-3 text-gray-800 dark:text-gray-200">Mike Johnson</td>
                <td className="py-3 text-gray-800 dark:text-gray-200">3 {isClient ? t('admin.itemsCount') : 'items'}</td>
                <td className="py-3 text-gray-800 dark:text-gray-200">$42.75</td>
                <td className="py-3"><span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full text-xs">{isClient ? t('admin.orderStatusTypes.new') : 'New'}</span></td>
                <td className="py-3"><button className="text-[#56999B] dark:text-[#5DBDC0]">{isClient ? t('admin.view') : 'View'}</button></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
} 