"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import { DeliveryZone, DeliveryZoneType } from "@/types/models";
import { getUserDeliveryZones } from "@/lib/firebase/firestore";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import dynamic from "next/dynamic";

// Dynamically import components to avoid SSR issues with UI components
const DeliveryZoneForm = dynamic(() => import("@/components/admin-dashboard/delivery-zones/DeliveryZoneForm"), { ssr: false });
const DeliveryZoneList = dynamic(() => import("@/components/admin-dashboard/delivery-zones/DeliveryZoneList"), { ssr: false });

// Define prop types for the dynamic components to fix TypeScript errors
interface DeliveryZoneListProps {
  zones: DeliveryZone[];
  loading: boolean;
  onEdit: (zone: DeliveryZone) => void;
  onDelete: (zoneId: string) => void;
}

interface DeliveryZoneFormProps {
  editingZone: DeliveryZone | null;
  onZoneAdded: (zone: DeliveryZone) => void;
  onZoneUpdated: (zone: DeliveryZone) => void;
  onCancel: () => void;
}

export default function DeliveryZonesPage() {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  const [zones, setZones] = useState<DeliveryZone[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>("all");
  const [editingZone, setEditingZone] = useState<DeliveryZone | null>(null);
  
  useEffect(() => {
    const fetchZones = async () => {
      if (user) {
        try {
          setLoading(true);
          const fetchedZones = await getUserDeliveryZones(user.uid);
          setZones(fetchedZones);
        } catch (error) {
          console.error("Error fetching delivery zones:", error);
        } finally {
          setLoading(false);
        }
      }
    };
    
    fetchZones();
  }, [user]);
  
  const handleZoneAdded = (newZone: DeliveryZone) => {
    setZones([...zones, newZone]);
  };
  
  const handleZoneUpdated = (updatedZone: DeliveryZone) => {
    setZones(zones.map(zone => zone.id === updatedZone.id ? updatedZone : zone));
    setEditingZone(null);
  };
  
  const handleZoneDeleted = (zoneId: string) => {
    setZones(zones.filter(zone => zone.id !== zoneId));
  };
  
  const filteredZones = activeTab === "all" 
    ? zones 
    : zones.filter(zone => zone.type === activeTab);

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-2xl font-bold mb-6">{isClient ? t('admin.deliveryZones.title') : 'Delivery Zones'}</h1>
      
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="all">{isClient ? t('admin.deliveryZones.tabs.all') : 'All Zones'}</TabsTrigger>
          <TabsTrigger value={DeliveryZoneType.PICK_UP}>{isClient ? t('admin.deliveryZoneTypes.pickUp') : 'Pick Up'}</TabsTrigger>
          <TabsTrigger value={DeliveryZoneType.DELIVERY}>{isClient ? t('admin.deliveryZoneTypes.delivery') : 'Delivery'}</TabsTrigger>
          <TabsTrigger value={DeliveryZoneType.IN_HOUSE_TABLES}>{isClient ? t('admin.deliveryZoneTypes.inHouseTables') : 'In-House Tables'}</TabsTrigger>
        </TabsList>
        
        <TabsContent value={activeTab} className="space-y-6">
          <DeliveryZoneList 
            zones={filteredZones} 
            loading={loading} 
            onEdit={setEditingZone} 
            onDelete={handleZoneDeleted} 
          />
        </TabsContent>
      </Tabs>
      
      <div className="mt-8 p-6 bg-white dark:bg-[#1d2127] rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold mb-4">
          {editingZone 
            ? isClient ? t('admin.updateZone') : 'Update Zone' 
            : isClient ? t('admin.addZone') : 'Add Zone'
          }
        </h2>
        <DeliveryZoneForm 
          editingZone={editingZone} 
          onZoneAdded={handleZoneAdded} 
          onZoneUpdated={handleZoneUpdated} 
          onCancel={() => setEditingZone(null)} 
        />
      </div>
    </div>
  );
} 