'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface UserData {
  uid: string;
  email: string | null;
  emailVerified: boolean;
  displayName: string | null;
  photoURL: string | null;
}

export default function ProtectedAdminPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [serverData, setServerData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !user) {
      router.push('/signin?from=/admin/protected-example');
      return;
    }
    
    // Fetch user data from server to validate session
    const fetchSessionData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/auth/session');
        
        if (!response.ok) {
          throw new Error('Failed to validate session');
        }
        
        const data = await response.json();
        
        if (!data.isAuthenticated) {
          router.push('/signin?from=/admin/protected-example');
          return;
        }
        
        setServerData(data.user);
      } catch (error) {
        console.error('Session validation error:', error);
        setError('Failed to validate your session. Please login again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    if (user) {
      fetchSessionData();
    }
  }, [user, loading, router]);
  
  // Show loading state
  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Loading...</h2>
          <p className="text-gray-500">Please wait while we verify your session</p>
        </div>
      </div>
    );
  }
  
  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center text-red-500">
          <h2 className="text-xl font-semibold mb-2">Authentication Error</h2>
          <p>{error}</p>
          <button 
            onClick={() => router.push('/signin')}
            className="mt-4 px-4 py-2 bg-primary text-white rounded"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Protected Admin Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Client-Side Auth Data</h2>
          {user && (
            <div className="space-y-2">
              <p><strong>User ID:</strong> {user.uid}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Email Verified:</strong> {user.emailVerified ? 'Yes' : 'No'}</p>
              <p><strong>Display Name:</strong> {user.displayName || 'Not set'}</p>
              {user.photoURL && (
                <div className="mt-2">
                  <p className="mb-1"><strong>Profile Photo:</strong></p>
                  <img 
                    src={user.photoURL} 
                    alt={user.displayName || 'User'} 
                    className="w-16 h-16 rounded-full"
                  />
                </div>
              )}
            </div>
          )}
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Server-Side Auth Data</h2>
          {serverData && (
            <div className="space-y-2">
              <p><strong>User ID:</strong> {serverData.uid}</p>
              <p><strong>Email:</strong> {serverData.email}</p>
              <p><strong>Email Verified:</strong> {serverData.emailVerified ? 'Yes' : 'No'}</p>
              <p><strong>Display Name:</strong> {serverData.displayName || 'Not set'}</p>
              {serverData.photoURL && (
                <div className="mt-2">
                  <p className="mb-1"><strong>Profile Photo:</strong></p>
                  <img 
                    src={serverData.photoURL} 
                    alt={serverData.displayName || 'User'} 
                    className="w-16 h-16 rounded-full"
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      
      <div className="mt-6">
        <button
          onClick={() => router.push('/admin')}
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded mr-2"
        >
          Back to Admin
        </button>
        <button
          onClick={async () => {
            try {
              await fetch('/api/auth/logout', { method: 'POST' });
              router.push('/signin');
            } catch (error) {
              console.error('Logout failed:', error);
            }
          }}
          className="px-4 py-2 bg-red-500 text-white rounded"
        >
          Logout
        </button>
      </div>
    </div>
  );
} 