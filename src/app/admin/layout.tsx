"use client";

import AdminDashboardLayout from "@/components/admin-dashboard/DashboardLayout";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { ReactNode } from "react";

export default function AdminLayout({ children }: { children: ReactNode }) {
  const { isClient } = useLocale();
  const { loading, isAdmin, user } = useAuth();
  const router = useRouter();
  const [isChecked, setIsChecked] = useState(false);
  
  // Check if user is an admin - only redirect once
  useEffect(() => {
    console.log("AdminLayout: Auth state check", { loading, isAdmin, userId: user?.uid });
    
    // Only check once when loading is complete
    if (!loading && !isChecked) {
      if (!isAdmin) {
        console.log("AdminLayout: Redirecting to customer dashboard - user is not admin");
        router.replace('/customer/dashboard');
      } else {
        console.log("AdminLayout: User is admin, rendering admin layout");
      }
      setIsChecked(true);
    }
  }, [loading, isAdmin, user, router, isChecked]);
  
  // Prevent layout flicker by not rendering until all checks are complete
  if (!isClient || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }
  
  // Don't render admin content if not an admin
  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }
  
  return <AdminDashboardLayout>{children}</AdminDashboardLayout>;
} 