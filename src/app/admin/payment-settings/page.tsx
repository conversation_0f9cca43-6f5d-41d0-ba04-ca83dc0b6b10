"use client";

import { useLocale } from "@/contexts/LocaleContext";

export default function PaymentSettingsPage() {
  const { t, isClient } = useLocale();
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          {isClient ? t('admin.paymentSettings') : 'Payment Settings'}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Configure payment methods and settings for your business
        </p>
      </div>
      
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6 flex flex-col items-center justify-center min-h-[300px]">
        <i className="fa-solid fa-credit-card text-5xl text-gray-300 dark:text-gray-600 mb-4"></i>
        <p className="text-gray-500 dark:text-gray-400 text-center max-w-md">
          {isClient ? t('admin.noDataMessage') : 'No data available yet'}
        </p>
      </div>
    </div>
  );
} 