"use client";

import { useEffect, useState } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import Link from "next/link";
import { DocumentSnapshot } from "firebase/firestore";
import { getUserMenuItems, getUserCategories, deleteMenuItem, updateCategoryItemCount } from "@/lib/firebase/firestore";
import { Category, MenuItem, StockStatus } from "@/types/models";
import { useAuth } from "@/contexts/AuthContext";

export default function MenuItemsPage() {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [lastVisible, setLastVisible] = useState<DocumentSnapshot | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  
  useEffect(() => {
    if (user?.uid) {
      loadCategories();
      loadMenuItems();
    }
  }, [user?.uid]);

  // Close the dropdown menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openMenuId && !(event.target as Element).closest('.menu-dropdown')) {
        setOpenMenuId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openMenuId]);
  
  const loadCategories = async () => {
    try {
      if (!user?.uid) return;
      
      const userCategories = await getUserCategories(user.uid);
      setCategories(userCategories);
    } catch (err) {
      console.error("Error loading categories:", err);
      setError("Failed to load categories");
    }
  };
  
  const loadMenuItems = async (refresh = true) => {
    try {
      setIsLoading(true);
      setError(null);
      
      if (!user?.uid) return;
      
      // If refresh is true, reset pagination
      if (refresh) {
        setLastVisible(null);
      }
      
      // Convert status filter to StockStatus enum if needed
      let stockStatusFilter: StockStatus | undefined = undefined;
      if (statusFilter === "in_stock") {
        stockStatusFilter = StockStatus.IN_STOCK;
      } else if (statusFilter === "low_stock") {
        stockStatusFilter = StockStatus.LOW_STOCK;
      } else if (statusFilter === "out_of_stock") {
        stockStatusFilter = StockStatus.OUT_OF_STOCK;
      }
      
      // Call Firestore function with filters
      const result = await getUserMenuItems(
        user.uid,
        10,
        refresh ? null : lastVisible,
        categoryFilter || undefined,
        stockStatusFilter,
        searchTerm || undefined
      );
      
      // If refreshing, replace items, otherwise append
      if (refresh) {
        setMenuItems(result.items);
      } else {
        setMenuItems(prev => [...prev, ...result.items]);
      }
      
      // Update pagination
      setLastVisible(result.lastVisible);
      setHasMore(result.items.length === 10); // If we got less than 10 items, there are no more
    } catch (err) {
      console.error("Error loading menu items:", err);
      setError("Failed to load menu items");
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleDeleteItem = async (itemId: string, categoryId: string) => {
    try {
      if (!user?.uid) return;
      
      setIsDeleting(itemId);
      
      // Delete the menu item
      await deleteMenuItem(itemId);
      
      // Remove from local state
      setMenuItems(prev => prev.filter(item => item.id !== itemId));
      
      // Update the category item count
      await updateCategoryItemCount(categoryId, user.uid);
      
      // Refresh the categories list
      await loadCategories();
      
      // Close the menu
      setOpenMenuId(null);
    } catch (err) {
      console.error("Error deleting menu item:", err);
      setError("Failed to delete menu item");
    } finally {
      setIsDeleting(null);
    }
  };
  
  const toggleMenu = (itemId: string) => {
    setOpenMenuId(prev => prev === itemId ? null : itemId);
  };
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCategoryFilter(e.target.value);
  };
  
  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
  };
  
  const handleSearch = () => {
    loadMenuItems(true);
  };
  
  const loadMore = () => {
    if (!isLoading && hasMore) {
      loadMenuItems(false);
    }
  };
  
  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : 'Unknown Category';
  };
  
  const getStockStatusText = (status: StockStatus) => {
    switch (status) {
      case StockStatus.IN_STOCK:
        return isClient ? t('admin.inStock') : 'In Stock';
      case StockStatus.LOW_STOCK:
        return isClient ? t('admin.lowStock') : 'Low Stock';
      case StockStatus.OUT_OF_STOCK:
        return isClient ? t('admin.outOfStock') : 'Out of Stock';
      default:
        return 'Unknown';
    }
  };
  
  const getStockStatusClass = (status: StockStatus) => {
    switch (status) {
      case StockStatus.IN_STOCK:
        return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400';
      case StockStatus.LOW_STOCK:
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400';
      case StockStatus.OUT_OF_STOCK:
        return 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400';
      default:
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400';
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{isClient ? t('admin.menuItems') : 'Menu Items'}</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">{isClient ? t('admin.manageMenuItems') : 'Manage your restaurant menu items'}</p>
        </div>
        <Link
          href="/admin/menu-items/add"
          className="inline-flex items-center gap-2 px-4 py-2 bg-[#56999B] dark:bg-[#5DBDC0] rounded-md text-white hover:bg-[#74C8CA] dark:hover:bg-[#4A9A9D] transition-colors shadow-sm"
        >
          <i className="fa-solid fa-plus text-xs"></i>
          <span>{isClient ? t('admin.addNewItem') : 'Add New Item'}</span>
        </Link>
      </div>
      
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 p-4 rounded-lg">
          {error}
        </div>
      )}
      
      <div className="bg-white dark:bg-[#1d2127] p-4 rounded-xl shadow-sm mb-6">
        <div className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-[200px]">
            <input 
              type="text" 
              value={searchTerm}
              onChange={handleSearchChange}
              placeholder={isClient ? t('admin.searchMenuItems') : 'Search menu items...'} 
              className="w-full px-4 py-2 border border-gray-200 dark:border-gray-700 dark:bg-[#2d3339] dark:text-gray-200 rounded-lg focus:outline-none focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <select 
            value={categoryFilter}
            onChange={handleCategoryChange}
            className="px-4 py-2 border border-gray-200 dark:border-gray-700 dark:bg-[#2d3339] dark:text-gray-200 rounded-lg focus:outline-none focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
          >
            <option value="">{isClient ? t('admin.allCategories') : 'All Categories'}</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          <select 
            value={statusFilter}
            onChange={handleStatusChange}
            className="px-4 py-2 border border-gray-200 dark:border-gray-700 dark:bg-[#2d3339] dark:text-gray-200 rounded-lg focus:outline-none focus:border-[#83EAED] dark:focus:border-[#5DBDC0]"
          >
            <option value="">{isClient ? t('admin.statusAll') : 'Status: All'}</option>
            <option value="in_stock">{isClient ? t('admin.inStock') : 'In Stock'}</option>
            <option value="low_stock">{isClient ? t('admin.lowStock') : 'Low Stock'}</option>
            <option value="out_of_stock">{isClient ? t('admin.outOfStock') : 'Out of Stock'}</option>
          </select>
          <button 
            onClick={handleSearch}
            className="px-4 py-2 bg-[#56999B] dark:bg-[#5DBDC0] text-white rounded-lg hover:bg-[#74C8CA] dark:hover:bg-[#4A9A9D]"
          >
            {isClient ? t('common.search') : 'Search'}
          </button>
        </div>
      </div>

      {isLoading && menuItems.length === 0 ? (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#56999B] dark:border-[#5DBDC0]"></div>
        </div>
      ) : menuItems.length === 0 ? (
        <div className="bg-white dark:bg-[#1d2127] p-8 rounded-xl shadow-sm text-center">
          <p className="text-gray-500 dark:text-gray-400">
            {isClient ? t('admin.noMenuItems') : 'No menu items found. Add your first item!'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {menuItems.map(item => (
            <div key={item.id} className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm overflow-hidden">
              <div className="relative h-48">
                <img 
                  className="w-full h-full object-cover" 
                  src={item.image || "https://storage.googleapis.com/uxpilot-auth.appspot.com/placeholder-food.png"} 
                  alt={item.title} 
                />
                <div className="absolute top-2 right-2">
                  <div className="relative menu-dropdown">
                    <button 
                      className="bg-white dark:bg-[#2d3339] p-2 rounded-full shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700"
                      onClick={() => toggleMenu(item.id)}
                    >
                      <i className="fa-solid fa-ellipsis-v text-gray-600 dark:text-gray-400"></i>
                    </button>
                    {openMenuId === item.id && (
                      <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-[#2d3339] rounded-md shadow-lg overflow-hidden z-20">
                        <Link 
                          href={`/admin/menu-items/edit/${item.id}`} 
                          className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          {isClient ? t('admin.editItem') : 'Edit Item'}
                        </Link>
                        <button 
                          className="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                          onClick={() => handleDeleteItem(item.id, item.categoryId)}
                          disabled={isDeleting === item.id}
                        >
                          {isDeleting === item.id 
                            ? (isClient ? t('common.deleting') : 'Deleting...') 
                            : (isClient ? t('admin.deleteItem') : 'Delete Item')}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-bold text-gray-800 dark:text-white">{item.title}</h3>
                  <span className="text-[#56999B] dark:text-[#5DBDC0] font-bold">${item.price.toFixed(2)}</span>
                </div>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-1">
                  {item.description}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500 mb-4">
                  {getCategoryName(item.categoryId)}
                </p>
                <div className="flex justify-between items-center">
                  <span className={`px-2 py-1 ${getStockStatusClass(item.stockStatus)} rounded-full text-xs`}>
                    {getStockStatusText(item.stockStatus)}
                  </span>
                  <Link
                    href={`/admin/menu-items/edit/${item.id}`}
                    className="text-[#56999B] dark:text-[#5DBDC0] hover:text-[#74C8CA] dark:hover:text-[#83EAED]"
                  >
                    {isClient ? t('admin.editItem') : 'Edit Item'}
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {hasMore && menuItems.length > 0 && (
        <div className="mt-8 flex justify-center">
          <button
            onClick={loadMore}
            disabled={isLoading}
            className="px-6 py-2 bg-white dark:bg-[#1d2127] border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-[#2d3339] disabled:opacity-50"
          >
            {isLoading 
              ? (isClient ? t('common.loading') : 'Loading...') 
              : (isClient ? t('admin.loadMore') : 'Load More')}
          </button>
        </div>
      )}
    </div>
  );
} 