"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import Logo from "@/components/ui/Logo";
import DarkModeToggle from "@/components/ui/DarkModeToggle";
import LanguageSwitcher from "@/components/ui/LanguageSwitcher";
import { toast } from "@/hooks/use-toast";

export default function VerifyEmailPage() {
  const { t, dir, isClient } = useLocale();
  const { user, loading, isEmailVerified, sendVerification, logout } = useAuth();
  const router = useRouter();
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // Check if user is authenticated and whether email is verified
  useEffect(() => {
    if (!loading) {
      if (!user) {
        // If no user, redirect to sign in
        router.push('/signin');
      } else if (isEmailVerified) {
        // If email is verified, redirect to dashboard
        router.push('/customer/dashboard');
      }
    }
  }, [loading, user, isEmailVerified, router]);

  // Handle countdown for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else if (countdown === 0 && resendDisabled) {
      setResendDisabled(false);
    }
  }, [countdown, resendDisabled]);

  const handleResendVerification = async () => {
    try {
      setResendDisabled(true);
      setCountdown(60); // 60 second cooldown
      await sendVerification();
      toast({
        variant: "success",
        title: "Verification email sent",
        description: "Please check your inbox and follow the instructions to verify your email."
      });
    } catch (error) {
      console.error("Failed to resend verification email:", error);
    }
  };

  const handleSignOut = async () => {
    try {
      await logout();
      router.push('/signin');
    } catch (error) {
      console.error("Failed to sign out:", error);
    }
  };

  // Display loading state while checking authentication
  if (loading || !user || isEmailVerified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-[#F9FDFC] dark:bg-[#171c23]">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div
      className="min-h-screen bg-[#F9FDFC] dark:bg-[#171c23] flex flex-col"
      dir={isClient ? dir : "ltr"}
    >
      {/* Header */}
      <header className="py-6 px-8">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Logo linkTo="/" />
          <div className="flex items-center gap-3">
            <DarkModeToggle />
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-white dark:bg-[#1d2127] rounded-xl shadow-md p-8">
          <div className="text-center mb-6">
            <div className="bg-yellow-100 dark:bg-yellow-900/20 w-20 h-20 flex items-center justify-center rounded-full mx-auto mb-4">
              <i className="fa-solid fa-envelope text-yellow-600 dark:text-yellow-500 text-3xl"></i>
            </div>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
              {isClient ? t('verifyEmail.title') : 'Verify Your Email'}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              {isClient ? t('verifyEmail.description') : 'We sent a verification email to:'}
            </p>
            <p className="text-primary font-medium mt-1">
              {user.email}
            </p>
          </div>

          <div className="space-y-4">
            <div className="bg-gray-50 dark:bg-gray-800/30 rounded-lg p-4 text-sm">
              <p className="text-gray-700 dark:text-gray-300">
                {isClient ? t('verifyEmail.instructions') : 'Please check your email and click on the verification link to activate your account. If you don\'t see the email, check your spam folder.'}
              </p>
            </div>

            <button 
              onClick={handleResendVerification}
              disabled={resendDisabled}
              className="w-full py-3 rounded-lg bg-primary text-white font-medium transition-colors hover:bg-primary/90 disabled:bg-gray-300 disabled:text-gray-500 dark:disabled:bg-gray-700 dark:disabled:text-gray-400"
            >
              {resendDisabled 
                ? `${isClient ? t('verifyEmail.resendDisabled') : 'Resend in'} ${countdown}s`
                : isClient ? t('verifyEmail.resend') : 'Resend Verification Email'
              }
            </button>

            <div className="mt-6 text-center">
              <button
                onClick={handleSignOut}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-sm"
              >
                {isClient ? t('verifyEmail.signOut') : 'Sign out and use a different account'}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 