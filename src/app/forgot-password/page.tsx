"use client";

import Link from "next/link";
import { useState } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import LanguageSwitcher from "@/components/ui/LanguageSwitcher";
import DarkModeToggle from "@/components/ui/DarkModeToggle";
import Logo from "@/components/ui/Logo";

export default function ForgotPasswordPage() {
  const { t, dir, isClient } = useLocale();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [formState, setFormState] = useState<"idle" | "success" | "error">("idle");
  
  // Dynamic RTL-aware classes
  const rtlAwareClasses = {
    textAlign: dir === 'rtl' ? 'text-right' : 'text-left',
    labelClass: `block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 ${dir === 'rtl' ? 'text-right' : 'text-left'}`,
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setFormState("error");
      setIsLoading(false);
      return;
    }
    
    // Simulate API call for password reset
    setTimeout(() => {
      setIsLoading(false);
      setFormState("success");
    }, 1000);
  };

  const renderFormStatus = () => {
    if (formState === "success") {
      return (
        <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-900 rounded-lg text-green-700 dark:text-green-300">
          {isClient ? t('forgotPassword.successMessage') : "If your email exists in our system, you will receive a password reset link shortly."}
        </div>
      );
    } else if (formState === "error") {
      return (
        <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-900 rounded-lg text-red-700 dark:text-red-300">
          {isClient ? t('forgotPassword.errorMessage') : "Please enter a valid email address."}
        </div>
      );
    }
    return null;
  };

  return (
    <div 
      className="min-h-screen bg-[#F9FDFC] dark:bg-[#171c23] flex flex-col" 
      dir={isClient ? dir : "ltr"}
    >
      {/* Header */}
      <header className="py-6 px-8">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Logo linkTo="/" />
          <div className="flex items-center gap-3">
            <DarkModeToggle />
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-md bg-white dark:bg-[#1d2127] rounded-2xl shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2">
              {isClient ? t('forgotPassword.title') : "Forgot Password"}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {isClient ? t('forgotPassword.subtitle') : "Enter your email address and we'll send you a password reset link"}
            </p>
          </div>

          <form className="space-y-4" onSubmit={handleSubmit}>
            <div className="space-y-1">
              <label className={rtlAwareClasses.labelClass} htmlFor="email">
                {isClient ? t('forgotPassword.emailLabel') : "Email address"}
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-[#66B8B1] dark:focus:ring-[#5DBDC0] focus:border-transparent ${rtlAwareClasses.textAlign}`}
                placeholder={isClient ? t('forgotPassword.emailPlaceholder') : "Enter your email"}
              />
            </div>

            {renderFormStatus()}

            <button
              type="submit"
              disabled={isLoading || formState === "success"}
              className="w-full bg-[#83EAED] dark:bg-[#5DBDC0] text-white py-3 rounded-lg hover:bg-[#6ED3D6] dark:hover:bg-[#4A9EA0] transition-colors font-medium flex justify-center items-center disabled:opacity-70 disabled:cursor-not-allowed mt-4"
            >
              {isLoading ? (
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : null}
              {isClient ? t('forgotPassword.submitButton') : "Send Reset Link"}
            </button>
          </form>

          <div className="mt-8 text-center">
            <Link href="/signin">
              <span className="text-[#66B8B1] dark:text-[#5DBDC0] hover:underline font-medium cursor-pointer inline-block">
                {isClient ? t('forgotPassword.backToLogin') : "Back to login"}
              </span>
            </Link>
          </div>
        </div>
      </main>

      <footer className="py-6 text-center text-gray-500 dark:text-gray-400 text-sm">
        <p>
          {isClient ? t('common.footer') : "© 2025 Barcode Cafe. All rights reserved."}
        </p>
      </footer>
    </div>
  );
} 