"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import LanguageSwitcher from "@/components/ui/LanguageSwitcher";
import DarkModeToggle from "@/components/ui/DarkModeToggle";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import Logo from "@/components/ui/Logo";

export default function SignUp() {
  const { t, dir, isClient } = useLocale();
  const { register, loginWithGoogle } = useAuth();
  const router = useRouter();
  
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    password: "",
    confirmPassword: "",
    termsAccepted: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    // Validation
    if (!formData.fullName.trim()) {
      setError("Please enter your full name");
      return;
    }
    
    if (!formData.email.trim()) {
      setError("Please enter your email address");
      return;
    }
    
    if (formData.password.length < 6) {
      setError("Password must be at least 6 characters");
      return;
    }
    
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords don't match");
      return;
    }
    
    if (!formData.termsAccepted) {
      setError("You must accept the Terms & Conditions");
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Register with Firebase
      await register(formData.email, formData.password);
      
      // Redirect to verification page after registration
      router.push('/signin');
    } catch (err: unknown) {
      console.error("Registration error:", err);
      
      // Handle known Firebase errors
      const firebaseError = err as { code?: string; message?: string };
      
      if (firebaseError.code === 'auth/email-already-in-use') {
        setError("This email is already registered. Please sign in instead.");
      } else if (firebaseError.code === 'auth/invalid-email') {
        setError("Please enter a valid email address.");
      } else if (firebaseError.code === 'auth/weak-password') {
        setError("Please choose a stronger password.");
      } else {
        setError(firebaseError.message || "Failed to create account. Please try again.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setError(null);
    setIsLoading(true);
    
    try {
      await loginWithGoogle();
      router.push('/customer/dashboard');
    } catch (err: unknown) {
      console.error("Google sign-up error:", err);
      const googleError = err as { message?: string };
      setError(googleError.message || "Failed to sign up with Google. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Dynamic RTL-aware classes
  const rtlAwareClasses = {
    textAlign: dir === 'rtl' ? 'text-right' : 'text-left',
    labelClass: `block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 ${dir === 'rtl' ? 'text-right' : 'text-left'}`,
    checkboxContainer: `flex items-center ${dir === 'rtl' ? 'flex-row-reverse justify-end' : 'justify-start'} gap-2`,
  };

  return (
    <div 
      className="min-h-screen bg-[#F9FDFC] dark:bg-[#171c23] flex flex-col" 
      dir={isClient ? dir : "ltr"}
    >
      {/* Header */}
      <header className="py-6 px-8">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Logo linkTo="/" />
          <div className="flex items-center gap-3">
            <DarkModeToggle />
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-md bg-white dark:bg-[#1d2127] rounded-2xl shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2">
              {isClient ? t('signup.title') : "Join the Barcode Cafe Community!"}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {isClient ? t('signup.subtitle') : "Create your account to get started"}
            </p>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg text-sm">
              {error}
            </div>
          )}

          <form className="space-y-4" onSubmit={handleSubmit}>
            <div className="space-y-1">
              <label className={rtlAwareClasses.labelClass} htmlFor="fullName">
                {isClient ? t('signup.fullNameLabel') : "Full Name"}
              </label>
              <input 
                type="text" 
                id="fullName" 
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                className={`w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-[#66B8B1] dark:focus:ring-[#5DBDC0] focus:border-transparent ${rtlAwareClasses.textAlign}`}
                placeholder={isClient ? t('signup.fullNamePlaceholder') : "Enter your full name"} 
              />
            </div>

            <div className="space-y-1">
              <label className={rtlAwareClasses.labelClass} htmlFor="email">
                {isClient ? t('signup.emailLabel') : "Email Address"}
              </label>
              <input 
                type="email" 
                id="email" 
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-[#66B8B1] dark:focus:ring-[#5DBDC0] focus:border-transparent ${rtlAwareClasses.textAlign}`}
                placeholder={isClient ? t('signup.emailPlaceholder') : "Enter your email"} 
              />
            </div>

            <div className="space-y-1">
              <label className={rtlAwareClasses.labelClass} htmlFor="password">
                {isClient ? t('signup.passwordLabel') : "Password"}
              </label>
              <input 
                type="password" 
                id="password" 
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-[#66B8B1] dark:focus:ring-[#5DBDC0] focus:border-transparent ${rtlAwareClasses.textAlign}`}
                placeholder={isClient ? t('signup.passwordPlaceholder') : "Create a password"} 
              />
            </div>

            <div className="space-y-1">
              <label className={rtlAwareClasses.labelClass} htmlFor="confirmPassword">
                {isClient ? t('signup.confirmPasswordLabel') : "Confirm Password"}
              </label>
              <input 
                type="password" 
                id="confirmPassword" 
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                className={`w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-[#66B8B1] dark:focus:ring-[#5DBDC0] focus:border-transparent ${rtlAwareClasses.textAlign}`}
                placeholder={isClient ? t('signup.confirmPasswordPlaceholder') : "Confirm your password"} 
              />
            </div>

            <div className={rtlAwareClasses.checkboxContainer}>
              <input 
                type="checkbox" 
                id="termsAccepted"
                name="termsAccepted"
                checked={formData.termsAccepted}
                onChange={handleChange}
                className="rounded border-gray-300 dark:border-gray-600 text-[#66B8B1] dark:text-[#5DBDC0] focus:ring-[#66B8B1] dark:focus:ring-[#5DBDC0]" 
              />
              <label htmlFor="termsAccepted" className={`text-sm text-gray-600 dark:text-gray-400 ${rtlAwareClasses.textAlign}`}>
                {isClient ? t('signup.termsCheckbox') : "I agree to the"} 
                <span className="text-[#66B8B1] dark:text-[#5DBDC0] hover:underline cursor-pointer mx-1 inline-block">
                  {isClient ? t('signup.termsLink') : "Terms & Conditions"}
                </span>
              </label>
            </div>

            <button 
              type="submit" 
              disabled={isLoading}
              className="w-full bg-[#83EAED] dark:bg-[#5DBDC0] text-white py-3 rounded-lg hover:bg-[#6ED3D6] dark:hover:bg-[#4A9EA0] transition-colors font-medium mt-6 flex justify-center items-center"
            >
              {isLoading ? (
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : null}
              {isClient ? t('signup.createAccountButton') : "Create Account"}
            </button>
          </form>

          <div className="mt-8">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-[#1d2127] text-gray-500 dark:text-gray-400">
                  {isClient ? t('signin.orSignInWith') : "Or continue with"}
                </span>
              </div>
            </div>

            <div className="mt-6 flex justify-center">
              <button 
                type="button"
                onClick={handleGoogleSignUp}
                disabled={isLoading}
                className={`flex ${dir === 'rtl' ? 'flex-row-reverse' : 'flex-row'} justify-center items-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-[#242832] transition-colors icon-exempt w-full max-w-[200px]`}
              >
                <i className="fa-brands fa-google text-xl text-gray-700 dark:text-gray-300 mx-2"></i>
                <span className="text-gray-700 dark:text-gray-300">
                  {isClient ? (dir === 'rtl' ? 'جوجل' : 'Google') : 'Google'}
                </span>
              </button>
            </div>
          </div>

          <div className="mt-8 text-center">
            <p className="text-gray-600 dark:text-gray-400 inline-block">
              {isClient ? t('signup.haveAccount') : "Already have an account?"} 
              <Link href="/signin">
                <span className="text-[#66B8B1] dark:text-[#5DBDC0] hover:underline font-medium cursor-pointer mx-1 inline-block">
                  {isClient ? t('signup.signIn') : "Sign in here"}
                </span>
              </Link>
            </p>
          </div>
        </div>
      </main>

      <footer className="py-6 text-center text-gray-500 dark:text-gray-400 text-sm">
        <p>
          {isClient ? t('common.footer') : "© 2025 Barcode Cafe. All rights reserved."}
        </p>
      </footer>
    </div>
  );
} 