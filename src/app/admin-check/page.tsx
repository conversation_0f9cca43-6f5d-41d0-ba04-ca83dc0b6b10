"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { isUserAdmin, getAdmin, createAdmin } from "@/lib/firebase/firestore";
import { useRouter } from "next/navigation";
import { auth } from "@/lib/firebase/config";

export default function AdminCheckPage() {
  const { user, isAdmin } = useAuth();
  const [directCheck, setDirectCheck] = useState<boolean | null>(null);
  const [adminDoc, setAdminDoc] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [createStatus, setCreateStatus] = useState<string | null>(null);
  const router = useRouter();

  // Add console logging for debugging
  useEffect(() => {
    console.log("AdminCheckPage: Auth state changed - isAdmin:", isAdmin);
  }, [isAdmin]);

  useEffect(() => {
    async function checkAdminDirectly() {
      if (user) {
        try {
          // Check admin status directly
          console.log("Checking admin status directly for:", user.uid);
          const adminStatus = await isUserAdmin(user.uid);
          console.log("Admin status result:", adminStatus);
          setDirectCheck(adminStatus);

          // Try to get the admin document
          const admin = await getAdmin(user.uid);
          setAdminDoc(admin);
        } catch (error) {
          console.error("Error checking admin:", error);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    }

    checkAdminDirectly();
  }, [user]);

  const handleCreateAdmin = async () => {
    if (!user) return;
    
    setCreateStatus("Creating admin...");
    try {
      // Create admin document with exact UID
      await createAdmin(user.uid, {
        email: user.email || '',
        displayName: user.displayName || 'Admin User',
        photoURL: user.photoURL || '',
      });
      
      // Refresh admin status
      const adminStatus = await isUserAdmin(user.uid);
      setDirectCheck(adminStatus);
      const admin = await getAdmin(user.uid);
      setAdminDoc(admin);
      
      setCreateStatus("Admin created successfully! Refresh the page to see if the AuthContext picks up the change.");
    } catch (error) {
      console.error("Error creating admin:", error);
      setCreateStatus(`Error creating admin: ${error}`);
    }
  };

  const handleForceReauth = async () => {
    if (!user) return;
    
    try {
      setCreateStatus("Refreshing authentication state...");
      
      // Force token refresh
      await user.getIdToken(true);
      
      // Force auth state reload
      await auth.currentUser?.reload();
      
      // Clear any cached data or local storage
      localStorage.removeItem("userData");
      sessionStorage.clear();
      
      // Redirect to admin dashboard
      router.push("/admin/dashboard");
    } catch (error) {
      console.error("Error refreshing auth:", error);
      setCreateStatus(`Error refreshing auth: ${error}`);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <div>Please sign in first</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Admin Status Check</h1>
      
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mb-4">
        <h2 className="text-xl font-semibold mb-2">User Information</h2>
        <div className="grid grid-cols-1 gap-2">
          <div>
            <span className="font-medium">UID:</span> {user.uid}
          </div>
          <div>
            <span className="font-medium">Email:</span> {user.email}
          </div>
          <div>
            <span className="font-medium">Display Name:</span> {user.displayName || 'Not set'}
          </div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mb-4">
        <h2 className="text-xl font-semibold mb-2">Admin Status</h2>
        <div className="grid grid-cols-1 gap-2">
          <div>
            <span className="font-medium">Context isAdmin:</span> {isAdmin ? "Yes" : "No"}
          </div>
          <div>
            <span className="font-medium">Direct Check:</span> {directCheck ? "Yes" : "No"}
          </div>
          {!directCheck && (
            <div className="mt-4">
              <button 
                onClick={handleCreateAdmin}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Create Admin Document
              </button>
            </div>
          )}
          <div className="mt-4">
            <button 
              onClick={handleForceReauth}
              className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Force Auth Refresh & Go To Admin Dashboard
            </button>
            {createStatus && (
              <div className="mt-2 text-sm">
                {createStatus}
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-2">Admin Document</h2>
        {adminDoc ? (
          <pre className="bg-gray-100 dark:bg-gray-900 p-2 rounded overflow-auto">
            {JSON.stringify(adminDoc, null, 2)}
          </pre>
        ) : (
          <div className="text-red-500">No admin document found</div>
        )}
      </div>
    </div>
  );
} 