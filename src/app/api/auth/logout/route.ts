import { NextResponse } from 'next/server';

/**
 * POST /api/auth/logout
 * Logs out the user by clearing the session cookie
 */
export async function POST() {
  try {
    // Create a response
    const response = NextResponse.json({ 
      success: true,
      message: 'Logged out successfully'
    });
    
    // Clear the session cookie
    response.cookies.delete('session');
    
    return response;
  } catch (error) {
    console.error('Logout error:', error);
    
    return NextResponse.json(
      { 
        success: false,
        message: 'Logout failed'
      }, 
      { status: 500 }
    );
  }
} 