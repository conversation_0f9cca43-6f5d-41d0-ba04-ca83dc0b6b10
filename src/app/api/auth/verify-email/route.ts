import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/firebase/admin';
import { cookies } from 'next/headers';

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

/**
 * POST /api/auth/verify-email
 * Checks if a user's email has been verified after clicking verification link in email
 */
export async function POST(request: NextRequest) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping verify-email API route execution during build');
    return NextResponse.json({ success: true, verified: false });
  }

  try {
    const { uid } = await request.json();
    
    if (!uid) {
      return NextResponse.json(
        { 
          success: false,
          verified: false,
          message: 'User ID is required'
        }, 
        { status: 400 }
      );
    }
    
    try {
      // Get the user by UID using Firebase Admin
      const userRecord = await auth.getUser(uid);
      
      // Return whether the email is verified
      return NextResponse.json({ 
        success: true,
        verified: userRecord.emailVerified
      });
    } catch (error) {
      console.error('Error checking email verification status:', error);
      
      return NextResponse.json(
        { 
          success: false,
          verified: false,
          message: 'Invalid user ID'
        }, 
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Server error in verify-email:', error);
    
    return NextResponse.json(
      { 
        success: false,
        verified: false,
        message: 'Server error'
      }, 
      { status: 500 }
    );
  }
} 