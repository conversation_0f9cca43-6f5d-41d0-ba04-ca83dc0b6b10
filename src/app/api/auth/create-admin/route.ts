import { NextRequest, NextResponse } from 'next/server';
import { auth, db } from '@/lib/firebase/admin';
import { createAdmin } from '@/lib/firebase/firestore';

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

/**
 * POST /api/auth/create-admin
 * Creates an admin user in Firebase Auth and in the admins Firestore collection
 * 
 * Note: This should be a protected route in a production environment
 */
export async function POST(request: NextRequest) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping create-admin API route execution during build');
    return NextResponse.json({ success: true, message: 'Admin user created successfully', uid: 'mock-uid' });
  }
  try {
    const { email, password, displayName } = await request.json();
    
    if (!email || !password) {
      return NextResponse.json(
        { 
          success: false,
          message: 'Email and password are required'
        }, 
        { status: 400 }
      );
    }
    
    try {
      // Create or update the user in Firebase Auth
      let userRecord;
      
      try {
        // Try to get existing user
        userRecord = await auth.getUserByEmail(email);
      } catch (error: unknown) {
        // User doesn't exist, create new user
        userRecord = await auth.createUser({
          email,
          password,
          displayName: displayName || '',
          emailVerified: true // Admin users are pre-verified
        });
      }
      
      // Create admin entry in Firestore
      await createAdmin(userRecord.uid, {
        email: userRecord.email || '',
        displayName: userRecord.displayName || '',
        photoURL: userRecord.photoURL || '',
      });
      
      return NextResponse.json({ 
        success: true,
        message: 'Admin user created successfully',
        uid: userRecord.uid
      });
    } catch (error: unknown) {
      console.error('Error creating admin user:', error);
      
      return NextResponse.json(
        { 
          success: false,
          message: 'Failed to create admin user'
        }, 
        { status: 500 }
      );
    }
  } catch (error: unknown) {
    console.error('Server error creating admin:', error);
    
    return NextResponse.json(
      { 
        success: false,
        message: 'Server error'
      }, 
      { status: 500 }
    );
  }
} 