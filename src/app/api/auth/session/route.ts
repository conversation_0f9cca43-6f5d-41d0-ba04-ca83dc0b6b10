import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/firebase/admin';

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

/**
 * GET /api/auth/session
 * Validates the current user session
 */
export async function GET(request: NextRequest) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping session API route execution during build');
    return NextResponse.json({ isAuthenticated: false, message: 'No session found' }, { status: 401 });
  }
  try {
    // Get the session cookie
    const sessionCookie = request.cookies.get('session')?.value;

    if (!sessionCookie) {
      return NextResponse.json(
        { 
          isAuthenticated: false,
          message: 'No session found'
        }, 
        { status: 401 }
      );
    }

    try {
      // Verify the session cookie with Firebase Admin
      const decodedClaims = await auth.verifyIdToken(sessionCookie);

      // Session is valid, return user data
      return NextResponse.json({ 
        isAuthenticated: true,
        user: {
          uid: decodedClaims.uid,
          email: decodedClaims.email,
          emailVerified: decodedClaims.email_verified,
          displayName: decodedClaims.name || null,
          photoURL: decodedClaims.picture || null,
        }
      });
    } catch (error) {
      // Token verification failed
      console.error('Session verification failed:', error);
      
      return NextResponse.json(
        { 
          isAuthenticated: false,
          message: 'Invalid session'
        }, 
        { status: 401 }
      );
    }
  } catch (error) {
    // Server error
    console.error('Error in session validation:', error);
    
    return NextResponse.json(
      { 
        isAuthenticated: false,
        message: 'Server error'
      }, 
      { status: 500 }
    );
  }
} 