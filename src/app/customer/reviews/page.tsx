"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/customer-dashboard/DashboardLayout";
import EmptyState from "@/components/ui/EmptyState";
import { getUserReviews, createReview, updateReview, deleteReview, getUserOrders } from "@/lib/firebase/firestore";
import { Review, Order, OrderStatus } from "@/types/models";
import { formatDate } from "@/lib/utils";

export default function ReviewsPage() {
  const router = useRouter();
  const { t, isClient } = useLocale();
  const { user, loading } = useAuth();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [completedOrders, setCompletedOrders] = useState<Order[]>([]);
  
  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  
  // Use refs for uncontrolled form inputs
  const [ratingValue, setRatingValue] = useState(5); // This needs to be state for star display
  const titleRef = useRef<HTMLInputElement>(null);
  const contentRef = useRef<HTMLTextAreaElement>(null);
  
  // Delete confirmation
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [reviewToDelete, setReviewToDelete] = useState<string | null>(null);
  
  // Fetch reviews and completed orders
  useEffect(() => {
    const fetchData = async () => {
      if (user?.uid) {
        setIsLoading(true);
        try {
          // Fetch user reviews
          const userReviews = await getUserReviews(user.uid);
          setReviews(userReviews);
          
          // Fetch completed orders
          const orders = await getUserOrders(user.uid);
          const completed = orders.filter(order => order.status === OrderStatus.DELIVERED);
          setCompletedOrders(completed);
        } catch (error) {
          console.error("Error fetching data:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };
    
    if (user) {
      fetchData();
    }
  }, [user]);
  
  // Redirect to signin if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/signin");
    } else if (user && !user.emailVerified) {
      // Redirect to verification page if email is not verified
      router.push("/verify-email");
    }
  }, [loading, user, router]);

  // Handle opening add review modal
  const handleWriteReview = (order?: Order) => {
    // Reset form state - only rating needs to be handled separately
    setRatingValue(5);
    
    if (order) {
      setSelectedOrder(order);
    } else {
      setSelectedOrder(null);
    }
    
    setIsAddModalOpen(true);
  };
  
  // Handle opening edit review modal
  const handleEditReview = (review: Review) => {
    setSelectedReview(review);
    
    // Set the rating state for the stars
    setRatingValue(review.rating);
    
    setIsEditModalOpen(true);
  };
  
  // Handle form submission for new review
  const handleAddSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.uid) return;
    
    try {
      setIsLoading(true);
      
      const newReview = await createReview({
        userId: user.uid,
        orderId: selectedOrder?.id || "no-order-id",
        rating: ratingValue,
        title: titleRef.current?.value || '',
        comment: contentRef.current?.value || '',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      setReviews(prev => [...prev, newReview]);
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Error adding review:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle form submission for updating a review
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedReview) return;
    
    try {
      setIsLoading(true);
      
      const updatedReview = await updateReview(selectedReview.id, {
        rating: ratingValue,
        title: titleRef.current?.value || '',
        comment: contentRef.current?.value || '',
        updatedAt: new Date()
      });
      
      setReviews(prev => 
        prev.map(review => review.id === selectedReview.id ? updatedReview : review)
      );
      setIsEditModalOpen(false);
    } catch (error) {
      console.error("Error updating review:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle delete confirmation
  const handleDeleteConfirm = (reviewId: string) => {
    setReviewToDelete(reviewId);
    setIsDeleteConfirmOpen(true);
  };
  
  // Handle actual deletion
  const handleDeleteReview = async () => {
    if (!reviewToDelete) return;
    
    try {
      setIsLoading(true);
      await deleteReview(reviewToDelete);
      setReviews(prev => prev.filter(review => review.id !== reviewToDelete));
      setIsDeleteConfirmOpen(false);
      setReviewToDelete(null);
    } catch (error) {
      console.error("Error deleting review:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Star Rating Component
  const StarRating = ({ 
    rating, 
    onStarClick,
    readOnly = false,
    size = "md" 
  }: { 
    rating: number; 
    onStarClick?: (rating: number) => void;
    readOnly?: boolean;
    size?: "sm" | "md" | "lg"; 
  }) => {
    const sizeClasses = {
      sm: "text-sm",
      md: "text-xl",
      lg: "text-2xl"
    };
    
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map(star => (
          <button
            key={star}
            type="button"
            onClick={() => !readOnly && onStarClick && onStarClick(star)}
            disabled={readOnly}
            className={`${sizeClasses[size]} ${readOnly ? 'cursor-default' : 'cursor-pointer'} focus:outline-none`}
          >
            <i 
              className={`fa-${star <= rating ? 'solid' : 'regular'} fa-star ${star <= rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`}>
            </i>
          </button>
        ))}
      </div>
    );
  };
  
  // Update the ReviewForm component
  const ReviewForm = ({ onSubmit }: { onSubmit: (e: React.FormEvent) => Promise<void> }) => {
    // Set default values when form mounts
    useEffect(() => {
      if (isAddModalOpen) {
        // Clear form when adding new review
        if (titleRef.current) titleRef.current.value = '';
        if (contentRef.current) contentRef.current.value = '';
      } else if (isEditModalOpen && selectedReview) {
        // Populate form with selected review data
        if (titleRef.current) titleRef.current.value = selectedReview.title || '';
        if (contentRef.current) contentRef.current.value = selectedReview.comment || '';
      }
    }, [isAddModalOpen, isEditModalOpen, selectedReview]);
    
    return (
      <form onSubmit={onSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t("reviews.title")}
          </label>
          <input
            type="text"
            name="title"
            ref={titleRef}
            placeholder={t("reviews.titlePlaceholder")}
            className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t("reviews.rating")}
          </label>
          <div className="flex items-center gap-4">
            <StarRating 
              rating={ratingValue} 
              onStarClick={setRatingValue} 
              size="lg" 
            />
            <span className="text-gray-500 dark:text-gray-400">{ratingValue}/5</span>
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            {t("reviews.content")}
          </label>
          <textarea
            name="content"
            ref={contentRef}
            placeholder={t("reviews.contentPlaceholder")}
            rows={5}
            className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
          />
        </div>
        
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={() => isAddModalOpen ? setIsAddModalOpen(false) : setIsEditModalOpen(false)}
            className="px-4 py-2 border dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-[#1d2127] dark:text-gray-300"
          >
            {t("common.cancel")}
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="bg-[#56999B] dark:bg-[#5DBDC0] text-white px-4 py-2 rounded-lg hover:bg-[#56999B]/90 dark:hover:bg-[#4A9EA0] disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isLoading && (
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
            )}
            {isAddModalOpen ? t("reviews.submitReview") : t("reviews.updateReview")}
          </button>
        </div>
      </form>
    );
  };
  
  // Modal Component
  const Modal = ({ 
    isOpen, 
    onClose, 
    title, 
    children 
  }: { 
    isOpen: boolean; 
    onClose: () => void; 
    title: string; 
    children: React.ReactNode 
  }) => {
    if (!isOpen) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
          <div className="p-6 border-b dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold dark:text-gray-100">{title}</h2>
              <button 
                className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                onClick={onClose}
              >
                <i className="fa-solid fa-xmark text-xl"></i>
              </button>
            </div>
          </div>
          <div className="p-6">
            {children}
          </div>
        </div>
      </div>
    );
  };
  
  // Delete Confirmation Modal
  const DeleteConfirmModal = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
    if (!isOpen) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-lg w-full max-w-md">
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4 dark:text-gray-100">{t("reviews.deleteConfirmTitle")}</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {t("reviews.deleteConfirmMessage")}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-[#1d2127] dark:text-gray-300"
              >
                {t("common.cancel")}
              </button>
              <button
                onClick={handleDeleteReview}
                disabled={isLoading}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isLoading && (
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                )}
                {t("reviews.delete")}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (!isClient) {
    return null;
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-100 dark:border-gray-800 flex justify-between items-center">
          <h1 className="text-2xl font-bold dark:text-gray-100">
            {t("nav.reviews")}
          </h1>
          <button 
            onClick={() => handleWriteReview()}
            className="bg-[#83EAED] dark:bg-[#5DBDC0] text-white px-4 py-2 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0] flex items-center"
          >
            <i className="fa-solid fa-pen mr-2"></i>
            {t("reviews.writeReview")}
          </button>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center p-16">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : reviews.length > 0 ? (
          <div className="divide-y divide-gray-100 dark:divide-gray-800">
            {reviews.map(review => (
              <div key={review.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="w-full">
                    <div className="flex items-center justify-between mb-2">
                      <StarRating rating={review.rating} readOnly={true} />
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {review.createdAt instanceof Date 
                          ? formatDate(review.createdAt) 
                          : typeof review.createdAt === 'string' 
                            ? formatDate(new Date(review.createdAt))
                            : ''}
                      </div>
                    </div>
                    
                    {review.comment && (
                      <p className="text-gray-600 dark:text-gray-400 mt-2 mb-4">{review.comment}</p>
                    )}
                    
                    <div className="flex space-x-2 justify-end mt-4">
                      <button 
                        onClick={() => handleEditReview(review)}
                        className="text-[#56999B] dark:text-[#5DBDC0] hover:text-[#4A9EA0] dark:hover:text-[#4A9EA0] text-sm"
                      >
                        <i className="fa-solid fa-pen-to-square mr-1"></i>
                        {t("reviews.edit")}
                      </button>
                      <button 
                        onClick={() => handleDeleteConfirm(review.id)}
                        className="text-red-500 hover:text-red-700 text-sm"
                      >
                        <i className="fa-solid fa-trash-can mr-1"></i>
                        {t("reviews.delete")}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <EmptyState
            icon="fa-solid fa-star"
            title={isClient ? t("customer.emptyStates.reviews.title") : "No Reviews"}
            message={isClient ? t("customer.emptyStates.reviews.message") : "You haven't written any reviews yet. Share your experience with our products!"}
            actionLabel={isClient ? t("customer.emptyStates.reviews.action") : "Write Review"}
            onAction={() => handleWriteReview()}
            className="py-16"
          />
        )}
      </div>
      
      {/* Past Orders that can be reviewed (optional section) */}
      {completedOrders.length > 0 && (
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm mt-6">
          <div className="p-6 border-b border-gray-100 dark:border-gray-800">
            <h2 className="text-xl font-bold dark:text-gray-100">
              {t("reviews.pastOrders")}
            </h2>
          </div>
          
          <div className="divide-y divide-gray-100 dark:divide-gray-800">
            {completedOrders.map(order => {
              // Check if order has already been reviewed
              const isReviewed = reviews.some(review => review.orderId === order.id);
              
              return (
                <div key={order.id} className="p-6 flex items-center justify-between">
                  <div>
                    <h3 className="font-medium dark:text-gray-100">
                      {order.items.length > 0 
                        ? `${order.items[0].name}${order.items.length > 1 ? ` + ${order.items.length - 1} ${t("customer.items.more")}` : ''}`
                        : t("customer.items.orderPlaced")}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {order.createdAt instanceof Date 
                        ? formatDate(order.createdAt) 
                        : typeof order.createdAt === 'string' 
                          ? formatDate(new Date(order.createdAt))
                          : ''}
                    </p>
                  </div>
                  
                  {isReviewed ? (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {t("reviews.alreadyReviewed")}
                    </span>
                  ) : (
                    <button 
                      onClick={() => handleWriteReview(order)}
                      className="text-[#56999B] dark:text-[#5DBDC0] text-sm hover:underline"
                    >
                      {t("reviews.reviewThisOrder")}
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
      
      {/* Add Review Modal */}
      <Modal 
        isOpen={isAddModalOpen} 
        onClose={() => setIsAddModalOpen(false)} 
        title={selectedOrder 
          ? t("reviews.reviewOrder", { orderName: selectedOrder.items[0]?.name || '' }) 
          : t("reviews.writeReview")
        }
      >
        <ReviewForm onSubmit={handleAddSubmit} />
      </Modal>
      
      {/* Edit Review Modal */}
      <Modal 
        isOpen={isEditModalOpen} 
        onClose={() => setIsEditModalOpen(false)} 
        title={t("reviews.editReview")}
      >
        <ReviewForm onSubmit={handleEditSubmit} />
      </Modal>
      
      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal 
        isOpen={isDeleteConfirmOpen} 
        onClose={() => {
          setIsDeleteConfirmOpen(false);
          setReviewToDelete(null);
        }} 
      />
    </DashboardLayout>
  );
} 