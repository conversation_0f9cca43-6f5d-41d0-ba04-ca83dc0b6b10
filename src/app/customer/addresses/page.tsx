"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/customer-dashboard/DashboardLayout";
import EmptyState from "@/components/ui/EmptyState";
import { getUserAddresses, createAddress, updateAddress, deleteAddress } from "@/lib/firebase/firestore";
import { Address } from "@/types/models";

export default function AddressesPage() {
  const router = useRouter();
  const { t, isClient } = useLocale();
  const { user, loading } = useAuth();
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Modal states
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  
  // References to form inputs - using uncontrolled components
  const nameRef = useRef<HTMLInputElement>(null);
  const addressLine1Ref = useRef<HTMLInputElement>(null);
  const addressLine2Ref = useRef<HTMLInputElement>(null);
  const cityRef = useRef<HTMLInputElement>(null);
  const stateRef = useRef<HTMLInputElement>(null);
  const postalCodeRef = useRef<HTMLInputElement>(null);
  const countryRef = useRef<HTMLInputElement>(null);
  const isDefaultRef = useRef<HTMLInputElement>(null);
  
  // Delete confirmation
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null);
  
  // Fetch addresses
  useEffect(() => {
    const fetchAddresses = async () => {
      if (user?.uid) {
        setIsLoading(true);
        try {
          const userAddresses = await getUserAddresses(user.uid);
          setAddresses(userAddresses);
        } catch (error) {
          console.error("Error fetching addresses:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };
    
    if (user) {
      fetchAddresses();
    }
  }, [user]);
  
  // Redirect to signin if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/signin");
    } else if (user && !user.emailVerified) {
      // Redirect to verification page if email is not verified
      router.push("/verify-email");
    }
  }, [loading, user, router]);

  // Handle opening add address modal
  const handleAddAddress = () => {
    setIsAddModalOpen(true);
  };
  
  // Handle opening edit address modal
  const handleEditAddress = (address: Address) => {
    setSelectedAddress(address);
    setIsEditModalOpen(true);
  };
  
  // Handle form submission for new address
  const handleAddSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.uid) return;
    
    try {
      setIsLoading(true);
      
      const newAddress = await createAddress({
        userId: user.uid,
        name: nameRef.current?.value || '',
        addressLine1: addressLine1Ref.current?.value || '',
        addressLine2: addressLine2Ref.current?.value || undefined,
        city: cityRef.current?.value || '',
        state: stateRef.current?.value || '',
        postalCode: postalCodeRef.current?.value || '',
        country: countryRef.current?.value || '',
        isDefault: isDefaultRef.current?.checked || false,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      setAddresses(prev => [...prev, newAddress]);
      setIsAddModalOpen(false);
    } catch (error) {
      console.error("Error adding address:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle form submission for updating an address
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.uid || !selectedAddress) return;
    
    try {
      setIsLoading(true);
      
      const updatedAddress = await updateAddress(selectedAddress.id, {
        name: nameRef.current?.value || '',
        addressLine1: addressLine1Ref.current?.value || '',
        addressLine2: addressLine2Ref.current?.value || undefined,
        city: cityRef.current?.value || '',
        state: stateRef.current?.value || '',
        postalCode: postalCodeRef.current?.value || '',
        country: countryRef.current?.value || '',
        isDefault: isDefaultRef.current?.checked || false
      });
      
      setAddresses(prev => 
        prev.map(addr => addr.id === selectedAddress.id ? updatedAddress : addr)
      );
      setIsEditModalOpen(false);
    } catch (error) {
      console.error("Error updating address:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle delete confirmation
  const handleDeleteConfirm = (addressId: string) => {
    setAddressToDelete(addressId);
    setIsDeleteConfirmOpen(true);
  };
  
  // Handle actual deletion
  const handleDeleteAddress = async () => {
    if (!addressToDelete) return;
    
    try {
      setIsLoading(true);
      await deleteAddress(addressToDelete);
      setAddresses(prev => prev.filter(addr => addr.id !== addressToDelete));
      setIsDeleteConfirmOpen(false);
      setAddressToDelete(null);
    } catch (error) {
      console.error("Error deleting address:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Form component using a uncontrolled inputs with refs
  const AddressForm = ({ onSubmit }: { onSubmit: (e: React.FormEvent) => Promise<void> }) => {
    // When the form mounts, set default values based on mode
    useEffect(() => {
      if (isAddModalOpen) {
        // Set default values for a new address
        if (nameRef.current) nameRef.current.value = '';
        if (addressLine1Ref.current) addressLine1Ref.current.value = '';
        if (addressLine2Ref.current) addressLine2Ref.current.value = '';
        if (cityRef.current) cityRef.current.value = '';
        if (stateRef.current) stateRef.current.value = '';
        if (postalCodeRef.current) postalCodeRef.current.value = '';
        if (countryRef.current) countryRef.current.value = '';
        if (isDefaultRef.current) isDefaultRef.current.checked = addresses.length === 0;
      } else if (isEditModalOpen && selectedAddress) {
        // Populate form with selected address data
        if (nameRef.current) nameRef.current.value = selectedAddress.name;
        if (addressLine1Ref.current) addressLine1Ref.current.value = selectedAddress.addressLine1;
        if (addressLine2Ref.current) addressLine2Ref.current.value = selectedAddress.addressLine2 || '';
        if (cityRef.current) cityRef.current.value = selectedAddress.city;
        if (stateRef.current) stateRef.current.value = selectedAddress.state;
        if (postalCodeRef.current) postalCodeRef.current.value = selectedAddress.postalCode;
        if (countryRef.current) countryRef.current.value = selectedAddress.country;
        if (isDefaultRef.current) isDefaultRef.current.checked = selectedAddress.isDefault;
      }
    }, [isAddModalOpen, isEditModalOpen, selectedAddress, addresses.length]);
    
    return (
      <form onSubmit={onSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t("addresses.name")}
          </label>
          <input
            type="text"
            name="name"
            ref={nameRef}
            placeholder={t("addresses.namePlaceholder")}
            required
            className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t("addresses.addressLine1")}
          </label>
          <input
            type="text"
            name="addressLine1"
            ref={addressLine1Ref}
            placeholder={t("addresses.addressLine1Placeholder")}
            required
            className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            {t("addresses.addressLine2")}
          </label>
          <input
            type="text"
            name="addressLine2"
            ref={addressLine2Ref}
            placeholder={t("addresses.addressLine2Placeholder")}
            className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("addresses.city")}
            </label>
            <input
              type="text"
              name="city"
              ref={cityRef}
              placeholder={t("addresses.cityPlaceholder")}
              required
              className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("addresses.state")}
            </label>
            <input
              type="text"
              name="state"
              ref={stateRef}
              placeholder={t("addresses.statePlaceholder")}
              required
              className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("addresses.postalCode")}
            </label>
            <input
              type="text"
              name="postalCode"
              ref={postalCodeRef}
              placeholder={t("addresses.postalCodePlaceholder")}
              required
              className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {t("addresses.country")}
            </label>
            <input
              type="text"
              name="country"
              ref={countryRef}
              placeholder={t("addresses.countryPlaceholder")}
              required
              className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
            />
          </div>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isDefault"
            name="isDefault"
            ref={isDefaultRef}
            className="h-4 w-4 text-[#83EAED] dark:text-[#5DBDC0] focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] border-gray-300 dark:border-gray-600 rounded"
          />
          <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
            {t("addresses.makeDefault")}
          </label>
        </div>
        
        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={() => isAddModalOpen ? setIsAddModalOpen(false) : setIsEditModalOpen(false)}
            className="px-4 py-2 border dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-[#1d2127] dark:text-gray-300"
          >
            {t("common.cancel")}
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="bg-[#56999B] dark:bg-[#5DBDC0] text-white px-4 py-2 rounded-lg hover:bg-[#56999B]/90 dark:hover:bg-[#4A9EA0] disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isLoading && (
              <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
            )}
            {isAddModalOpen ? t("addresses.addAddress") : t("addresses.saveChanges")}
          </button>
        </div>
      </form>
    );
  };

  // Modal Component
  const Modal = ({ 
    isOpen, 
    onClose, 
    title, 
    children 
  }: { 
    isOpen: boolean; 
    onClose: () => void; 
    title: string; 
    children: React.ReactNode 
  }) => {
    if (!isOpen) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
          <div className="p-6 border-b dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold dark:text-gray-100">{title}</h2>
              <button 
                className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                onClick={onClose}
              >
                <i className="fa-solid fa-xmark text-xl"></i>
              </button>
            </div>
          </div>
          <div className="p-6">
            {children}
          </div>
        </div>
      </div>
    );
  };
  
  // Delete Confirmation Modal
  const DeleteConfirmModal = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
    if (!isOpen) return null;
    
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-lg w-full max-w-md">
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4 dark:text-gray-100">{t("addresses.deleteConfirmTitle")}</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {t("addresses.deleteConfirmMessage")}
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-[#1d2127] dark:text-gray-300"
              >
                {t("common.cancel")}
              </button>
              <button
                onClick={handleDeleteAddress}
                disabled={isLoading}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isLoading && (
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                )}
                {t("addresses.delete")}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (!isClient) {
    return null;
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-100 dark:border-gray-800 flex justify-between items-center">
          <h1 className="text-2xl font-bold dark:text-gray-100">
            {t("nav.addresses")}
          </h1>
          <button 
            onClick={handleAddAddress}
            className="bg-[#83EAED] dark:bg-[#5DBDC0] text-white px-4 py-2 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0] flex items-center"
          >
            <i className="fa-solid fa-plus mr-2"></i>
            {t("addresses.addAddress")}
          </button>
        </div>
        
        {isLoading && addresses.length === 0 ? (
          <div className="flex justify-center p-16">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : addresses.length > 0 ? (
          <div className="divide-y divide-gray-100 dark:divide-gray-800">
            {addresses.map(address => (
              <div key={address.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium dark:text-gray-100">{address.name}</h3>
                      {address.isDefault && (
                        <span className="bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 text-[#56999B] dark:text-[#5DBDC0] text-xs px-2 py-1 rounded-full">
                          {t("addresses.default")}
                        </span>
                      )}
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">{address.addressLine1}</p>
                    {address.addressLine2 && (
                      <p className="text-gray-600 dark:text-gray-400">{address.addressLine2}</p>
                    )}
                    <p className="text-gray-600 dark:text-gray-400">
                      {address.city}, {address.state} {address.postalCode}
                    </p>
                    <p className="text-gray-600 dark:text-gray-400">{address.country}</p>
                  </div>
                  <div className="flex space-x-2">
                    <button 
                      onClick={() => handleEditAddress(address)}
                      className="text-[#56999B] dark:text-[#5DBDC0] hover:text-[#4A9EA0] dark:hover:text-[#4A9EA0]"
                    >
                      <i className="fa-solid fa-pen-to-square"></i>
                    </button>
                    <button 
                      onClick={() => handleDeleteConfirm(address.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <i className="fa-solid fa-trash-can"></i>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <EmptyState
            icon="fa-solid fa-location-dot"
            title={isClient ? t("customer.emptyStates.addresses.title") : "No Saved Addresses"}
            message={isClient ? t("customer.emptyStates.addresses.message") : "You don't have any saved addresses. Add your delivery addresses for faster checkout."}
            actionLabel={isClient ? t("customer.emptyStates.addresses.action") : "Add Address"}
            onAction={handleAddAddress}
            className="py-16"
          />
        )}
      </div>
      
      {/* Add Address Modal */}
      <Modal 
        isOpen={isAddModalOpen} 
        onClose={() => setIsAddModalOpen(false)} 
        title={t("addresses.addAddress")}
      >
        <AddressForm onSubmit={handleAddSubmit} />
      </Modal>
      
      {/* Edit Address Modal */}
      <Modal 
        isOpen={isEditModalOpen} 
        onClose={() => setIsEditModalOpen(false)} 
        title={t("addresses.editAddress")}
      >
        <AddressForm onSubmit={handleEditSubmit} />
      </Modal>
      
      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal 
        isOpen={isDeleteConfirmOpen} 
        onClose={() => {
          setIsDeleteConfirmOpen(false);
          setAddressToDelete(null);
        }} 
      />
    </DashboardLayout>
  );
}