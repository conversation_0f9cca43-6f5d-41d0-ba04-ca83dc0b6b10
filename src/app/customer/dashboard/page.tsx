"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/customer-dashboard/DashboardLayout";
import ProfileEditModal from "@/components/customer-dashboard/ProfileEditModal";
import UserAvatar from "@/components/ui/UserAvatar";
import EmptyState from "@/components/ui/EmptyState";
import Link from "next/link";
import { getUserProfile, getUserOrders } from "@/lib/firebase/firestore";
import { UserProfile, Order, OrderStatus } from "@/types/models";
import { formatDate } from "@/lib/utils";

export default function CustomerDashboardPage() {
  const router = useRouter();
  const { t, isClient } = useLocale();
  const { user, loading, isAdmin } = useAuth();
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Use user's photo URL from Firebase if available
  const userAvatarSrc = user?.photoURL || undefined;
  const displayName = user?.displayName || userProfile?.displayName || 'User';
  const userEmail = user?.email || userProfile?.email || '';

  // Fetch user profile and orders
  useEffect(() => {
    const fetchUserData = async () => {
      if (user?.uid) {
        setIsLoading(true);
        try {
          // Fetch user profile
          const profile = await getUserProfile(user.uid);
          if (profile) {
            setUserProfile(profile);
          } else {
            console.log("No user profile found");
          }
          
          // Fetch recent orders
          const orders = await getUserOrders(user.uid, 3);
          setRecentOrders(orders);
        } catch (error) {
          console.error("Error fetching user data:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };
    
    if (user) {
      fetchUserData();
    }
  }, [user]);

  // Redirect to signin if not authenticated
  useEffect(() => {
    console.log("CustomerDashboard: Checking redirect conditions", { 
      loading, 
      userExists: !!user, 
      emailVerified: user?.emailVerified, 
      isAdmin 
    });
    
    // Define a function to handle redirects only once
    const handleRedirect = () => {
      if (!loading) {
        if (!user) {
          console.log("CustomerDashboard: Redirecting to signin - no user");
          router.replace("/signin");
        } else if (user && !user.emailVerified) {
          console.log("CustomerDashboard: Redirecting to verify-email - email not verified");
          // Redirect to verification page if email is not verified
          router.replace("/verify-email");
        } else if (isAdmin) {
          console.log("CustomerDashboard: Redirecting to admin dashboard - user is admin");
          // Redirect to admin dashboard if user is an admin
          router.replace("/admin/dashboard");
        } else {
          console.log("CustomerDashboard: No redirect needed - user is customer");
        }
      }
    };
    
    // Small timeout to ensure state is correctly updated
    const redirectTimer = setTimeout(handleRedirect, 100);
    
    return () => clearTimeout(redirectTimer);
  }, [loading, user, isAdmin, router]);

  const handleBrowseMenu = () => {
    router.push("/menu");
  };

  if (!isClient) {
    return null;
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      {/* Profile Edit Modal */}
      <ProfileEditModal 
        isOpen={isProfileModalOpen} 
        onClose={() => setIsProfileModalOpen(false)} 
        userAvatarSrc={userAvatarSrc}
        userProfile={userProfile}
      />

      {/* Profile Summary */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <div className="flex items-center gap-6">
          <UserAvatar 
            src={userAvatarSrc}
            alt={displayName}
            size="lg"
          />
          <div>
            <h1 className="text-2xl font-bold dark:text-gray-100">{displayName}</h1>
            <p className="text-gray-500 dark:text-gray-400">
              {userEmail}
            </p>
          </div>
          <button 
            className="ml-auto bg-[#83EAED] dark:bg-[#5DBDC0] text-white px-6 py-2 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0]"
            onClick={() => setIsProfileModalOpen(true)}
          >
            {isClient ? t('customer.editProfile') : 'Edit Profile'}
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
          <div className="flex items-center gap-4">
            <div className="bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 p-3 rounded-lg">
              <i className="fa-solid fa-coins text-[#56999B] dark:text-[#5DBDC0] text-xl"></i>
            </div>
            <div>
              <h3 className="text-gray-500 dark:text-gray-400">{t("customer.loyaltyPoints")}</h3>
              <p className="text-2xl font-bold dark:text-gray-100">{userProfile?.loyaltyPoints || 0}</p>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
          <div className="flex items-center gap-4">
            <div className="bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 p-3 rounded-lg">
              <i className="fa-solid fa-mug-hot text-[#56999B] dark:text-[#5DBDC0] text-xl"></i>
            </div>
            <div>
              <h3 className="text-gray-500 dark:text-gray-400">{t("customer.totalOrders")}</h3>
              <p className="text-2xl font-bold dark:text-gray-100">{recentOrders.length || 0}</p>
            </div>
          </div>
        </div>
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
          <div className="flex items-center gap-4">
            <div className="bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 p-3 rounded-lg">
              <i className="fa-solid fa-gift text-[#56999B] dark:text-[#5DBDC0] text-xl"></i>
            </div>
            <div>
              <h3 className="text-gray-500 dark:text-gray-400">{t("customer.giftCards.title")}</h3>
              <p className="text-2xl font-bold dark:text-gray-100">$0.00</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold dark:text-gray-100">{t("customer.recentOrders")}</h2>
          <Link href="/customer/orders" className="text-[#56999B] dark:text-[#5DBDC0] hover:underline cursor-pointer">
            {t("customer.viewAll")}
          </Link>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center p-8">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : recentOrders.length > 0 ? (
          <div className="space-y-4">
            {recentOrders.map(order => (
              <div key={order.id} className="flex items-center justify-between p-4 hover:bg-gray-50 dark:hover:bg-[#242832] rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 p-3 rounded-lg">
                    <i className={`fa-solid ${order.items[0] ? 'fa-coffee' : 'fa-receipt'} text-[#56999B] dark:text-[#5DBDC0]`}></i>
                  </div>
                  <div>
                    <h4 className="font-medium dark:text-gray-100">
                      {order.items[0]?.name || t("customer.items.orderPlaced")}
                      {order.items.length > 1 && ` + ${order.items.length - 1} ${t("customer.items.more")}`}
                    </h4>
                    <p className="text-gray-500 dark:text-gray-400">
                      {order.createdAt instanceof Date 
                        ? formatDate(order.createdAt) 
                        : typeof order.createdAt === 'string' 
                          ? formatDate(new Date(order.createdAt))
                          : ''}
                    </p>
                  </div>
                </div>
                <div>
                  <span className="font-medium dark:text-gray-100">${order.total.toFixed(2)}</span>
                  <span className={`ml-2 text-xs px-2 py-1 rounded-full ${getStatusBadgeClass(order.status)}`}>
                    {getOrderStatusLabel(order.status, t)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <EmptyState
            icon="fa-solid fa-receipt"
            title={isClient ? t("customer.emptyStates.recentOrders.title") : "No Recent Orders"}
            message={isClient ? t("customer.emptyStates.recentOrders.message") : "You haven't placed any orders recently. Check out our menu to order something delicious!"}
            actionLabel={isClient ? t("customer.emptyStates.recentOrders.action") : "Browse Menu"}
            onAction={handleBrowseMenu}
            className="py-8"
          />
        )}
      </div>

      {/* Loyalty Program */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-bold mb-6 dark:text-gray-100">{t("customer.loyaltyProgramStatus")}</h2>
        <div className="relative pt-1">
          {/* Assuming 3000 points is needed for Gold status */}
          {(() => {
            const currentPoints = userProfile?.loyaltyPoints || 0;
            const totalPointsNeeded = 3000;
            const progressPercentage = Math.min(Math.round((currentPoints / totalPointsNeeded) * 100), 100);
            const pointsRemaining = totalPointsNeeded - currentPoints;
            
            return (
              <>
                <div className="flex mb-2 items-center justify-between">
                  <div>
                    <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 text-[#56999B] dark:text-[#5DBDC0]">
                      {t("customer.progressToGoldStatus")}
                    </span>
                  </div>
                  <div className="text-right">
                    <span className="text-xs font-semibold inline-block text-[#56999B] dark:text-[#5DBDC0]">
                      {progressPercentage}%
                    </span>
                  </div>
                </div>
                <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20">
                  <div 
                    style={{ width: `${progressPercentage}%` }} 
                    className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-[#56999B] dark:bg-[#5DBDC0]"
                  ></div>
                </div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {isClient 
                    ? t('customer.loyaltyProgressMessage').replace('{{points}}', pointsRemaining.toString())
                    : `Earn ${pointsRemaining} more points to reach Gold Status and unlock exclusive rewards!`
                  }
                </p>
              </>
            );
          })()}
        </div>
      </div>
    </DashboardLayout>
  );
}

// Helper functions for order status
function getOrderStatusLabel(status: OrderStatus, t: (key: string) => string): string {
  switch (status) {
    case OrderStatus.ORDER_PLACED:
      return t("orders.status.orderPlaced");
    case OrderStatus.PREPARING:
      return t("orders.status.preparing");
    case OrderStatus.READY_FOR_PICKUP:
      return t("orders.status.readyForPickup");
    case OrderStatus.OUT_FOR_DELIVERY:
      return t("orders.status.outForDelivery");
    case OrderStatus.DELIVERED:
      return t("orders.status.delivered");
    case OrderStatus.CANCELLED:
      return t("orders.status.cancelled");
    default:
      return status;
  }
}

function getStatusBadgeClass(status: OrderStatus): string {
  switch (status) {
    case OrderStatus.ORDER_PLACED:
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500";
    case OrderStatus.PREPARING:
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
    case OrderStatus.READY_FOR_PICKUP:
      return "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-500";
    case OrderStatus.OUT_FOR_DELIVERY:
      return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500";
    case OrderStatus.DELIVERED:
      return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
    case OrderStatus.CANCELLED:
      return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
  }
} 