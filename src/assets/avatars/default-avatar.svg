<svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Subtle gradient background -->
  <circle cx="100" cy="100" r="100" fill="url(#gradient-bg)"/>
  
  <!-- Upper body silhouette -->
  <path d="M100 115C126.51 115 148 93.5097 148 67C148 40.4903 126.51 19 100 19C73.4903 19 52 40.4903 52 67C52 93.5097 73.4903 115 100 115Z" fill="#5DBDC0"/>
  
  <!-- Lower body silhouette with rounded bottom edge -->
  <path d="M39 167.5C39 134.655 66.4446 108 100 108C133.555 108 161 134.655 161 167.5V170C161 178.837 153.837 186 145 186H55C46.1634 186 39 178.837 39 170V167.5Z" fill="#5DBDC0"/>
  
  <!-- Subtle highlight -->
  <path d="M100 105C121.539 105 139 87.5391 139 66C139 44.4609 121.539 27 100 27C78.4609 27 61 44.4609 61 66C61 87.5391 78.4609 105 100 105Z" fill="url(#gradient-highlight)"/>
  
  <!-- Define gradients -->
  <defs>
    <linearGradient id="gradient-bg" x1="0" y1="0" x2="200" y2="200" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#83EAED" stop-opacity="0.3"/>
      <stop offset="1" stop-color="#83EAED" stop-opacity="0.1"/>
    </linearGradient>
    <linearGradient id="gradient-highlight" x1="61" y1="27" x2="139" y2="105" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#5DBDC0"/>
      <stop offset="1" stop-color="#6BCFD2"/>
    </linearGradient>
  </defs>
</svg>