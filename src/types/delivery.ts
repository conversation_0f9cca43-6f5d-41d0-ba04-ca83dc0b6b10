// Delivery option types for checkout
import { DeliveryZone } from './models';

export enum DeliveryType {
  TABLE = 'table',
  PICK_UP = 'pick_up',
  DELIVERY = 'delivery'
}

export interface DeliveryOption {
  type: DeliveryType;
  // For TABLE type
  tableNumber?: string;
  tableZone?: DeliveryZone;
  // For DELIVERY type
  deliveryZone?: DeliveryZone;
  deliveryAddress?: string;
  // Common fields
  fee: number;
}
