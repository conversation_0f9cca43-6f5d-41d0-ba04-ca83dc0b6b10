import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  UserCredential,
  User,
  sendEmailVerification,
  sendPasswordResetEmail,
  ActionCodeSettings
} from 'firebase/auth';
import { auth } from './config';
import Cookies from 'js-cookie';

// Constants
const SESSION_COOKIE_NAME = 'session';
const SESSION_EXPIRY_DAYS = 14;

// Action code settings for email verification
const actionCodeSettings: ActionCodeSettings = {
  // URL you want to redirect back to after email verification
  url: process.env.NEXT_PUBLIC_BASE_URL 
    ? `${process.env.NEXT_PUBLIC_BASE_URL}/auth/action` 
    : 'http://localhost:3000/auth/action',
  // Handle the verification link in the app if possible
  handleCodeInApp: true,
  // iOS app settings (if you have an iOS app)
  iOS: {
    bundleId: 'com.barcodecafe.app'
  },
  // Android app settings (if you have an Android app)
  android: {
    packageName: 'com.barcodecafe.app',
    installApp: true,
    minimumVersion: '12'
  },
  // Don't require dynamic links (optional)
  dynamicLinkDomain: undefined
}

/**
 * Register a new user with email and password
 */
export const registerWithEmailAndPassword = async (
  email: string, 
  password: string
): Promise<UserCredential> => {
  return createUserWithEmailAndPassword(auth, email, password);
};

/**
 * Send email verification to user
 */
export const sendVerificationEmail = async (user: User): Promise<void> => {
  return sendEmailVerification(user, actionCodeSettings);
};

/**
 * Login with email and password
 */
export const loginWithEmailAndPassword = async (
  email: string, 
  password: string
): Promise<UserCredential> => {
  return signInWithEmailAndPassword(auth, email, password);
};

/**
 * Login with Google
 */
export const loginWithGoogle = async (): Promise<UserCredential> => {
  const provider = new GoogleAuthProvider();
  return signInWithPopup(auth, provider);
};

/**
 * Send password reset email
 */
export const resetPassword = async (email: string): Promise<void> => {
  return sendPasswordResetEmail(auth, email);
};

/**
 * Logout the current user
 */
export const logout = async (): Promise<void> => {
  Cookies.remove(SESSION_COOKIE_NAME);
  return signOut(auth);
};

/**
 * Set the session cookie with the user's ID token
 */
export const setSessionCookie = async (user: User): Promise<void> => {
  try {
    const idToken = await user.getIdToken();
    
    // Store the ID token in the session cookie
    Cookies.set(SESSION_COOKIE_NAME, idToken, { 
      expires: SESSION_EXPIRY_DAYS,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
  } catch (error) {
    console.error('Error setting session cookie:', error);
    throw error;
  }
};

/**
 * Get the session cookie
 */
export const getSessionCookie = (): string | undefined => {
  return Cookies.get(SESSION_COOKIE_NAME);
};

/**
 * Remove the session cookie
 */
export const removeSessionCookie = (): void => {
  Cookies.remove(SESSION_COOKIE_NAME);
}; 