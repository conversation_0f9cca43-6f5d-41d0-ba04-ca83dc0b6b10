// Firebase Admin SDK configuration for server-side operations

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

// Define types for our exports
interface MockAuth {
  getUser: (uid: string) => Promise<any>;
  getUserByEmail: (email: string) => Promise<any>;
  verifyIdToken: (token: string) => Promise<any>;
  generateEmailVerificationLink: (email: string, actionCodeSettings?: any) => Promise<string>;
  createUser: (userData: any) => Promise<any>;
  setCustomUserClaims: (uid: string, claims: any) => Promise<void>;
}

interface MockDb {
  collection: (name: string) => any;
}

// Export variables
let app: any;
let auth: MockAuth;
let db: MockDb;

if (isBuildTime) {
  // Create mock implementations for build time
  console.log('Using mock Firebase Admin implementation during build');
  
  // Mock app
  app = {};
  
  // Mock auth with required methods
  auth = {
    getUser: async () => ({
      uid: 'mock-uid',
      email: '<EMAIL>',
      emailVerified: true,
      displayName: 'Mock User'
    }),
    getUserByEmail: async () => ({
      uid: 'mock-uid',
      email: '<EMAIL>',
      emailVerified: true,
      displayName: 'Mock User'
    }),
    verifyIdToken: async () => ({
      uid: 'mock-uid',
      email: '<EMAIL>',
      email_verified: true,
      name: 'Mock User'
    }),
    generateEmailVerificationLink: async () => 'https://example.com/verify',
    createUser: async () => ({ uid: 'new-mock-uid' }),
    setCustomUserClaims: async () => {}
  };
  
  // Mock firestore with collection method
  db = {
    collection: () => ({
      doc: () => ({
        get: async () => ({
          exists: false,
          data: () => ({})
        }),
        set: async () => ({})
      }),
      where: () => ({
        get: async () => ({
          empty: true,
          docs: []
        })
      })
    })
  };
} else {
  try {
    // Only import Firebase Admin modules when not in build time
    const admin = require('firebase-admin');
    const { cert, getApps, initializeApp } = require('firebase-admin/app');
    const { getAuth } = require('firebase-admin/auth');
    const { getFirestore } = require('firebase-admin/firestore');
    
    // Function to initialize Firebase Admin
    function initializeFirebaseAdmin() {
      // Check if an instance of Firebase Admin is already running
      if (getApps().length > 0) {
        return getApps()[0];
      }
      
      // Load the service account key JSON file
      let serviceAccount;
      try {
        serviceAccount = JSON.parse(
          process.env.FIREBASE_SERVICE_ACCOUNT_KEY as string
        );
      } catch (error) {
        console.error('Error parsing Firebase service account key:', error);
        throw new Error('Firebase Admin initialization failed');
      }

      // Initialize Firebase Admin
      return initializeApp({
        credential: cert(serviceAccount),
        databaseURL: process.env.FIREBASE_DATABASE_URL,
      });
    }
    
    // Initialize Admin App
    app = initializeFirebaseAdmin();

    // Get Auth and Firestore instances
    auth = getAuth(app);
    db = getFirestore(app);
  } catch (error) {
    console.error('Error initializing Firebase Admin:', error);
    
    // Provide mock implementations as fallback
    app = {};
    auth = {
      getUser: async () => ({
        uid: 'error-mock-uid',
        email: '<EMAIL>',
        emailVerified: true,
        displayName: 'Error Mock User'
      }),
      getUserByEmail: async () => ({
        uid: 'error-mock-uid',
        email: '<EMAIL>',
        emailVerified: true,
        displayName: 'Error Mock User'
      }),
      verifyIdToken: async () => ({
        uid: 'error-mock-uid',
        email: '<EMAIL>',
        email_verified: true,
        name: 'Error Mock User'
      }),
      generateEmailVerificationLink: async () => 'https://example.com/verify',
      createUser: async () => ({ uid: 'error-new-mock-uid' }),
      setCustomUserClaims: async () => {}
    };
    db = {
      collection: () => ({
        doc: () => ({
          get: async () => ({
            exists: false,
            data: () => ({})
          }),
          set: async () => ({})
        }),
        where: () => ({
          get: async () => ({
            empty: true,
            docs: []
          })
        })
      })
    };
  }
}

export { app, auth, db };