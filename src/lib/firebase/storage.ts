import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { storage } from './config';

/**
 * Uploads a file to Firebase Storage
 * @param file The file to upload
 * @param path The path in Firebase Storage (e.g., 'menu-items/abc123.jpg')
 * @returns The download URL of the uploaded file
 */
export async function uploadFile(file: File, path: string): Promise<string> {
  try {
    // Create a storage reference
    const storageRef = ref(storage, path);
    
    // Upload the file
    const snapshot = await uploadBytes(storageRef, file);
    
    // Get and return the download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
}

/**
 * Generate a unique filename for uploaded files
 * @param userId User ID
 * @param originalFilename Original filename
 * @returns A unique filename with timestamp
 */
export function generateUniqueFileName(userId: string, originalFilename: string): string {
  const timestamp = Date.now();
  const extension = originalFilename.split('.').pop();
  return `${userId}_${timestamp}.${extension}`;
} 