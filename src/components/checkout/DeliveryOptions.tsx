'use client';

import { useState, useEffect } from 'react';
import { useLocale } from '@/contexts/LocaleContext';
import { DeliveryType, DeliveryOption } from '@/types/delivery';
import { DeliveryZone, DeliveryZoneType } from '@/types/models';
import { getDeliveryZonesByType } from '@/lib/firebase/firestore';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';

interface DeliveryOptionsProps {
  onDeliveryOptionSelected: (option: DeliveryOption) => void;
  selectedOption?: DeliveryOption;
}

export default function DeliveryOptions({ onDeliveryOptionSelected, selectedOption }: DeliveryOptionsProps) {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  const [selectedType, setSelectedType] = useState<DeliveryType>(selectedOption?.type || DeliveryType.PICK_UP);
  const [tableZones, setTableZones] = useState<DeliveryZone[]>([]); 
  const [deliveryZones, setDeliveryZones] = useState<DeliveryZone[]>([]);
  const [selectedTableZoneId, setSelectedTableZoneId] = useState<string>(selectedOption?.tableZone?.id || '');
  const [selectedTableNumber, setSelectedTableNumber] = useState<string>(selectedOption?.tableNumber || '');
  const [selectedZoneId, setSelectedZoneId] = useState<string>(selectedOption?.deliveryZone?.id || '');
  const [deliveryAddress, setDeliveryAddress] = useState(selectedOption?.deliveryAddress || '');
  const [loading, setLoading] = useState(false);

  // Fetch delivery zones and table zones when component mounts
  useEffect(() => {
    const fetchZones = async () => {
      try {
        setLoading(true);
        console.log('Fetching delivery zones');
        console.log('DeliveryZoneType.DELIVERY value:', DeliveryZoneType.DELIVERY);
        
        // Fetch delivery zones of type DELIVERY - these are added by admin, not user-specific
        const deliveryZonesData = await getDeliveryZonesByType(DeliveryZoneType.DELIVERY);
        console.log('Delivery zones data retrieved:', deliveryZonesData);
        
        setDeliveryZones(deliveryZonesData); // Already filtered for active zones in the query
        
        // If there are delivery zones and no zone is selected, select the first one by default
        if (deliveryZonesData.length > 0 && !selectedZoneId && selectedType === DeliveryType.DELIVERY) {
          setSelectedZoneId(deliveryZonesData[0].id);
        }
        
        console.log('Fetching table zones - DeliveryZoneType.IN_HOUSE_TABLES value:', DeliveryZoneType.IN_HOUSE_TABLES);
        // Fetch table zones of type IN_HOUSE_TABLES - these are added by admin, not user-specific
        const tableZonesData = await getDeliveryZonesByType(DeliveryZoneType.IN_HOUSE_TABLES);
        console.log('Table zones data retrieved:', tableZonesData);
        
        setTableZones(tableZonesData); // Already filtered for active zones in the query
        
        // If there are table zones and no zone is selected, select the first one by default
        if (tableZonesData.length > 0 && !selectedTableZoneId && selectedType === DeliveryType.TABLE) {
          setSelectedTableZoneId(tableZonesData[0].id);
        }
      } catch (error) {
        console.error('Error fetching zones:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchZones();
  }, [selectedType]);

  // Update the delivery option whenever relevant state changes
  useEffect(() => {
    let deliveryOption: DeliveryOption = {
      type: selectedType,
      fee: 0
    };

    switch (selectedType) {
      case DeliveryType.TABLE:
        const selectedTableZone = tableZones.find(zone => zone.id === selectedTableZoneId);
        if (selectedTableZone) {
          deliveryOption.tableZone = selectedTableZone;
          deliveryOption.tableNumber = selectedTableNumber;
        }
        break;
      case DeliveryType.DELIVERY:
        const selectedZone = deliveryZones.find(zone => zone.id === selectedZoneId);
        if (selectedZone) {
          deliveryOption.deliveryZone = selectedZone;
          deliveryOption.deliveryAddress = deliveryAddress;
          deliveryOption.fee = selectedZone.deliveryFee || 0;
        }
        break;
      case DeliveryType.PICK_UP:
      default:
        // No additional fields needed for pick up
        break;
    }

    onDeliveryOptionSelected(deliveryOption);
  }, [selectedType, selectedTableZoneId, selectedTableNumber, selectedZoneId, deliveryAddress, deliveryZones, tableZones, onDeliveryOptionSelected]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-[#703f23] dark:text-[#e09a62]">
        {isClient ? t('checkout.deliveryOptions') : 'Delivery Options'}
      </h3>
      
      <RadioGroup
        value={selectedType}
        onValueChange={(value) => setSelectedType(value as DeliveryType)}
        className="space-y-3"
      >
        <div className="flex items-center space-x-2">
          <RadioGroupItem value={DeliveryType.TABLE} id="table" />
          <Label htmlFor="table" className="text-[#703f23] dark:text-[#e09a62]">
            {isClient ? t('checkout.tableNumber') : 'Table Number'}
          </Label>
        </div>
        
        <div className="flex items-center space-x-2">
          <RadioGroupItem value={DeliveryType.PICK_UP} id="pickup" />
          <Label htmlFor="pickup" className="text-[#703f23] dark:text-[#e09a62]">
            {isClient ? t('checkout.pickUp') : 'Pick Up'}
          </Label>
        </div>
        
        <div className="flex items-center space-x-2">
          <RadioGroupItem value={DeliveryType.DELIVERY} id="delivery" />
          <Label htmlFor="delivery" className="text-[#703f23] dark:text-[#e09a62]">
            {isClient ? t('checkout.delivery') : 'Delivery'}
          </Label>
        </div>
      </RadioGroup>
      
      {/* Conditional fields based on selected delivery type */}
      {selectedType === DeliveryType.TABLE && (
        <Card className="bg-[#f8f5e9] dark:bg-[#2c3436] border-[#dac6ae] dark:border-[#392e23]">
          <CardContent className="pt-6 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="table-zone" className="text-[#703f23] dark:text-[#e09a62]">
                {isClient ? t('checkout.selectTableZone') : 'Select Table Zone'}
              </Label>
              <Select
                value={selectedTableZoneId}
                onValueChange={setSelectedTableZoneId}
                disabled={false} // Always enable the dropdown
              >
                <SelectTrigger className="max-w-xs bg-white dark:bg-[#392e23] border-[#dac6ae] dark:border-[#392e23]">
                  <SelectValue placeholder={
                    loading 
                      ? isClient ? t('common.loading') : 'Loading...'
                      : isClient ? t('checkout.selectTableZone') : 'Select a table zone'
                  } />
                </SelectTrigger>
                <SelectContent>
                  {tableZones.map((zone) => (
                    <SelectItem key={zone.id} value={zone.id}>
                      {zone.name}
                    </SelectItem>
                  ))}
                  {!loading && tableZones.length === 0 && (
                    <SelectItem value="no-zones" disabled>
                      {isClient ? t('checkout.noTableZones') : 'No table zones available'}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>

            {selectedTableZoneId && (
              <div className="space-y-2">
                <Label htmlFor="table-number" className="text-[#703f23] dark:text-[#e09a62]">
                  {isClient ? t('checkout.selectTableNumber') : 'Select Table Number'}
                </Label>
                <Select
                  value={selectedTableNumber}
                  onValueChange={setSelectedTableNumber}
                  disabled={!selectedTableZoneId || tableZones.length === 0}
                >
                  <SelectTrigger className="max-w-xs bg-white dark:bg-[#392e23] border-[#dac6ae] dark:border-[#392e23]">
                    <SelectValue placeholder={isClient ? t('checkout.selectTable') : 'Select a table'} />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedTableZoneId && tableZones.find(zone => zone.id === selectedTableZoneId)?.tableNumbers?.map((table) => (
                      <SelectItem key={table} value={table}>
                        {table}
                      </SelectItem>
                    ))}
                    {(!selectedTableZoneId || !tableZones.find(zone => zone.id === selectedTableZoneId)?.tableNumbers?.length) && (
                      <SelectItem value="no-tables" disabled>
                        {isClient ? t('checkout.noTablesAvailable') : 'No tables available'}
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            )}
          </CardContent>
        </Card>
      )}
      
      {selectedType === DeliveryType.DELIVERY && (
        <Card className="bg-[#f8f5e9] dark:bg-[#2c3436] border-[#dac6ae] dark:border-[#392e23]">
          <CardContent className="pt-6 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="delivery-zone" className="text-[#703f23] dark:text-[#e09a62]">
                {isClient ? t('checkout.selectDeliveryZone') : 'Select Delivery Zone'}
              </Label>
              <Select
                value={selectedZoneId}
                onValueChange={setSelectedZoneId}
                disabled={false} // Always enable the dropdown
              >
                <SelectTrigger className="max-w-xs bg-white dark:bg-[#392e23] border-[#dac6ae] dark:border-[#392e23]">
                  <SelectValue placeholder={
                    loading 
                      ? isClient ? t('common.loading') : 'Loading...'
                      : isClient ? t('checkout.selectDeliveryZone') : 'Select a delivery zone'
                  } />
                </SelectTrigger>
                <SelectContent>
                  {deliveryZones.map((zone) => (
                    <SelectItem key={zone.id} value={zone.id}>
                      {zone.name} {zone.deliveryFee && zone.deliveryFee > 0 
                        ? `(${isClient ? t('common.currency') : 'SAR'} ${zone.deliveryFee.toFixed(2)})`
                        : ''
                      }
                    </SelectItem>
                  ))}
                  {!loading && deliveryZones.length === 0 && (
                    <SelectItem value="no-zones" disabled>
                      {isClient ? t('checkout.noDeliveryZones') : 'No delivery zones available'}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              
              {selectedZoneId && deliveryZones.find(zone => zone.id === selectedZoneId)?.deliveryFee && (
                <p className="text-sm text-[#94795e] dark:text-[#b49678]">
                  {isClient ? t('checkout.deliveryFee') : 'Delivery Fee'}: {isClient ? t('common.currency') : 'SAR'} 
                  {deliveryZones.find(zone => zone.id === selectedZoneId)?.deliveryFee?.toFixed(2)}
                </p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="delivery-address" className="text-[#703f23] dark:text-[#e09a62]">
                {isClient ? t('checkout.deliveryAddress') : 'Delivery Address'}
              </Label>
              <Input
                id="delivery-address"
                value={deliveryAddress}
                onChange={(e) => setDeliveryAddress(e.target.value)}
                placeholder={isClient ? t('checkout.addressPlaceholder') : 'Enter your full address'}
                className="bg-white dark:bg-[#392e23] border-[#dac6ae] dark:border-[#392e23]"
              />
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
