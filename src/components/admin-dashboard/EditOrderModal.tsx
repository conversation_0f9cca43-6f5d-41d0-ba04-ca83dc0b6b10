"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/contexts/AuthContext";
import { Order, OrderItem, MenuItem, DeliveryZoneType, OrderStatus } from "@/types/models";
import { 
  editOrder, 
  canEditOrder, 
  calculateOrderTotals, 
  getAllActiveMenuItems,
  getDeliveryZonesByType 
} from "@/lib/firebase/firestore";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface EditOrderModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onOrderUpdated: (updatedOrder: Order) => void;
}

export default function EditOrderModal({
  order,
  isOpen,
  onClose,
  onOrderUpdated
}: EditOrderModalProps) {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [editReason, setEditReason] = useState<string>("");
  
  // Form state
  const [editedItems, setEditedItems] = useState<OrderItem[]>([]);
  const [deliveryType, setDeliveryType] = useState<DeliveryZoneType | undefined>();
  const [tableNumber, setTableNumber] = useState<string>("");
  const [deliveryAddress, setDeliveryAddress] = useState<string>("");
  const [specialInstructions, setSpecialInstructions] = useState<string>("");
  
  // Available data
  const [availableMenuItems, setAvailableMenuItems] = useState<MenuItem[]>([]);
  const [deliveryZones, setDeliveryZones] = useState<any[]>([]);
  
  // Calculated totals
  const [subtotal, setSubtotal] = useState(0);
  const [deliveryFee, setDeliveryFee] = useState(0);
  const [total, setTotal] = useState(0);

  // Initialize form when order changes
  useEffect(() => {
    if (order && isOpen) {
      initializeForm();
      checkEditability();
      loadAvailableData();
    }
  }, [order, isOpen]);

  // Recalculate totals when items change
  useEffect(() => {
    if (editedItems.length > 0) {
      const totals = calculateOrderTotals(editedItems, deliveryFee);
      setSubtotal(totals.subtotal);
      setTotal(totals.total);
    }
  }, [editedItems, deliveryFee]);

  const initializeForm = () => {
    if (!order) return;
    
    setEditedItems([...order.items]);
    setDeliveryType(order.deliveryType);
    setTableNumber(order.tableNumber || "");
    setDeliveryAddress(order.deliveryAddress || "");
    setSpecialInstructions(order.specialInstructions || "");
    setSubtotal(order.subtotal);
    setDeliveryFee(order.deliveryFee);
    setTotal(order.total);
  };

  const checkEditability = async () => {
    if (!order) return;
    
    try {
      const result = await canEditOrder(order);
      setCanEdit(result.canEdit);
      setEditReason(result.reason || "");
    } catch (error) {
      console.error("Error checking order editability:", error);
      setCanEdit(false);
      setEditReason("Error checking order status");
    }
  };

  const loadAvailableData = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      // Load menu items
      const menuItems = await getAllActiveMenuItems(user.uid);
      setAvailableMenuItems(menuItems);
      
      // Load delivery zones
      const zones = await getDeliveryZonesByType(DeliveryZoneType.DELIVERY);
      setDeliveryZones(zones);
    } catch (error) {
      console.error("Error loading available data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleItemQuantityChange = (itemIndex: number, newQuantity: number) => {
    if (newQuantity < 1) {
      // Remove item if quantity is 0
      setEditedItems(prev => prev.filter((_, index) => index !== itemIndex));
    } else {
      setEditedItems(prev => 
        prev.map((item, index) => 
          index === itemIndex ? { ...item, quantity: newQuantity } : item
        )
      );
    }
  };

  const handleAddMenuItem = (menuItem: MenuItem) => {
    const newOrderItem: OrderItem = {
      id: menuItem.id,
      name: menuItem.title,
      price: menuItem.price,
      quantity: 1,
      options: []
    };
    
    setEditedItems(prev => [...prev, newOrderItem]);
  };

  const handleRemoveItem = (itemIndex: number) => {
    setEditedItems(prev => prev.filter((_, index) => index !== itemIndex));
  };

  const handleSave = async () => {
    if (!order || !user || !canEdit) return;
    
    setIsSaving(true);
    try {
      const editData = {
        items: editedItems,
        deliveryType,
        tableNumber: tableNumber || undefined,
        deliveryAddress: deliveryAddress || undefined,
        specialInstructions: specialInstructions || undefined,
        subtotal,
        deliveryFee,
        total
      };

      const updatedOrder = await editOrder(
        order.id,
        editData,
        user.uid,
        user.email || ""
      );

      onOrderUpdated(updatedOrder);
      onClose();
    } catch (error) {
      console.error("Error saving order:", error);
      // TODO: Show error toast
    } finally {
      setIsSaving(false);
    }
  };

  if (!order) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold dark:text-gray-100">
            {isClient ? t("admin.editOrder") : "Edit Order"} #{order.id.slice(-8).toUpperCase()}
          </DialogTitle>
        </DialogHeader>

        {!canEdit ? (
          <div className="p-6 text-center">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <i className="fa-solid fa-exclamation-triangle text-red-500 text-2xl mb-2"></i>
              <h3 className="text-lg font-medium text-red-800 dark:text-red-200 mb-2">
                {isClient ? t("admin.cannotEditOrder") : "Cannot Edit Order"}
              </h3>
              <p className="text-red-600 dark:text-red-300">
                {editReason}
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Order Items Section */}
            <div>
              <h3 className="text-lg font-medium mb-4 dark:text-gray-100">
                {isClient ? t("admin.orderItems") : "Order Items"}
              </h3>
              
              <div className="space-y-3">
                {editedItems.map((item, index) => (
                  <div key={`${item.id}-${index}`} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-[#242832] rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium dark:text-gray-100">{item.name}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {isClient ? t('common.currency') : 'SAR'} {item.price.toFixed(2)} each
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleItemQuantityChange(index, item.quantity - 1)}
                          className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center"
                        >
                          <i className="fa-solid fa-minus text-sm"></i>
                        </button>
                        
                        <span className="w-8 text-center font-medium dark:text-gray-100">
                          {item.quantity}
                        </span>
                        
                        <button
                          onClick={() => handleItemQuantityChange(index, item.quantity + 1)}
                          className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center"
                        >
                          <i className="fa-solid fa-plus text-sm"></i>
                        </button>
                      </div>
                      
                      <button
                        onClick={() => handleRemoveItem(index)}
                        className="text-red-500 hover:text-red-700 p-2"
                      >
                        <i className="fa-solid fa-trash text-sm"></i>
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Add Menu Item */}
              <div className="mt-4">
                <h4 className="font-medium mb-2 dark:text-gray-100">
                  {isClient ? t("admin.addMenuItem") : "Add Menu Item"}
                </h4>
                <Select onValueChange={(value) => {
                  const menuItem = availableMenuItems.find(item => item.id === value);
                  if (menuItem) handleAddMenuItem(menuItem);
                }}>
                  <SelectTrigger>
                    <SelectValue placeholder={isClient ? t("admin.selectMenuItem") : "Select a menu item to add"} />
                  </SelectTrigger>
                  <SelectContent>
                    {availableMenuItems.map((item) => (
                      <SelectItem key={item.id} value={item.id}>
                        {item.title} - {isClient ? t('common.currency') : 'SAR'} {item.price.toFixed(2)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Delivery Information */}
            <div>
              <h3 className="text-lg font-medium mb-4 dark:text-gray-100">
                {isClient ? t("admin.deliveryInformation") : "Delivery Information"}
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2 dark:text-gray-100">
                    {isClient ? t("checkout.deliveryType") : "Delivery Type"}
                  </label>
                  <Select value={deliveryType} onValueChange={(value) => setDeliveryType(value as DeliveryZoneType)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={DeliveryZoneType.PICK_UP}>
                        {isClient ? t("checkout.pickUp") : "Pick Up"}
                      </SelectItem>
                      <SelectItem value={DeliveryZoneType.DELIVERY}>
                        {isClient ? t("checkout.delivery") : "Delivery"}
                      </SelectItem>
                      <SelectItem value={DeliveryZoneType.IN_HOUSE_TABLES}>
                        {isClient ? t("admin.deliveryZoneTypes.inHouseTables") : "Table Service"}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {deliveryType === DeliveryZoneType.IN_HOUSE_TABLES && (
                  <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-100">
                      {isClient ? t("checkout.tableNumber") : "Table Number"}
                    </label>
                    <Input
                      value={tableNumber}
                      onChange={(e) => setTableNumber(e.target.value)}
                      placeholder={isClient ? t("checkout.enterTableNumber") : "Enter table number"}
                    />
                  </div>
                )}

                {deliveryType === DeliveryZoneType.DELIVERY && (
                  <div>
                    <label className="block text-sm font-medium mb-2 dark:text-gray-100">
                      {isClient ? t("checkout.deliveryAddress") : "Delivery Address"}
                    </label>
                    <Textarea
                      value={deliveryAddress}
                      onChange={(e) => setDeliveryAddress(e.target.value)}
                      placeholder={isClient ? t("checkout.addressPlaceholder") : "Enter delivery address"}
                      rows={3}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Special Instructions */}
            <div>
              <label className="block text-sm font-medium mb-2 dark:text-gray-100">
                {isClient ? t("orders.specialInstructions") : "Special Instructions"}
              </label>
              <Textarea
                value={specialInstructions}
                onChange={(e) => setSpecialInstructions(e.target.value)}
                placeholder={isClient ? t("orders.specialInstructions") : "Enter any special instructions"}
                rows={3}
              />
            </div>

            {/* Order Summary */}
            <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
              <h3 className="text-lg font-medium mb-3 dark:text-gray-100">
                {isClient ? t("orders.summary") : "Order Summary"}
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {isClient ? t("orders.subtotal") : "Subtotal"}
                  </span>
                  <span className="dark:text-gray-100">
                    {isClient ? t('common.currency') : 'SAR'} {subtotal.toFixed(2)}
                  </span>
                </div>
                {deliveryFee > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      {isClient ? t("checkout.deliveryFee") : "Delivery Fee"}
                    </span>
                    <span className="dark:text-gray-100">
                      {isClient ? t('common.currency') : 'SAR'} {deliveryFee.toFixed(2)}
                    </span>
                  </div>
                )}
                <div className="flex justify-between font-medium text-lg border-t pt-2">
                  <span className="dark:text-gray-100">
                    {isClient ? t("orders.total") : "Total"}
                  </span>
                  <span className="dark:text-gray-100">
                    {isClient ? t('common.currency') : 'SAR'} {total.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isSaving}
              >
                {isClient ? t("common.cancel") : "Cancel"}
              </Button>
              <Button
                onClick={handleSave}
                disabled={isSaving || isLoading}
                className="bg-[#56999B] hover:bg-[#74C8CA] text-white"
              >
                {isSaving ? (
                  <>
                    <i className="fa-solid fa-spinner fa-spin mr-2"></i>
                    {isClient ? t("common.saving") : "Saving..."}
                  </>
                ) : (
                  <>
                    <i className="fa-solid fa-save mr-2"></i>
                    {isClient ? t("admin.saveChanges") : "Save Changes"}
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
