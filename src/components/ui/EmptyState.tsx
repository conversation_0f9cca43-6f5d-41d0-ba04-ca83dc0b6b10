"use client";

import React from 'react';

interface EmptyStateProps {
  icon: string;
  title: string;
  message: string;
  actionLabel?: string;
  onAction?: () => void;
  className?: string;
}

/**
 * A reusable empty state component for sections with no data
 * 
 * @param icon - FontAwesome icon class
 * @param title - Title text for the empty state
 * @param message - Descriptive message explaining the empty state
 * @param actionLabel - Optional label for action button
 * @param onAction - Optional callback for action button
 * @param className - Optional additional CSS classes
 */
export default function EmptyState({
  icon,
  title,
  message,
  actionLabel,
  onAction,
  className = ""
}: EmptyStateProps) {
  return (
    <div className={`flex flex-col items-center justify-center p-8 ${className}`}>
      <div className="bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 w-20 h-20 flex items-center justify-center rounded-full mb-4">
        <i className={`${icon} text-[#56999B] dark:text-[#5DBDC0] text-3xl`}></i>
      </div>
      <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2 text-center">
        {title}
      </h3>
      <p className="text-gray-500 dark:text-gray-400 text-center max-w-md mb-6">
        {message}
      </p>
      {actionLabel && onAction && (
        <button
          onClick={onAction}
          className="bg-[#83EAED] dark:bg-[#5DBDC0] text-white px-6 py-2 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0]"
        >
          {actionLabel}
        </button>
      )}
    </div>
  );
} 