"use client";

import Link from "next/link";

interface LogoProps {
  withText?: boolean;
  textSize?: "sm" | "md" | "lg";
  iconSize?: "sm" | "md" | "lg";
  linkTo?: string;
}

export default function Logo({ 
  withText = true, 
  textSize = "md", 
  iconSize = "md",
  linkTo = "/"
}: LogoProps) {
  // Map text sizes to Tailwind classes
  const textSizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl"
  };
  
  // Map icon sizes to Tailwind classes
  const iconSizeClasses = {
    sm: "text-xl",
    md: "text-2xl",
    lg: "text-3xl"
  };
  
  const logoContent = (
    <div className="flex items-center gap-2 logo-container icon-exempt">
      <i className={`fa-solid fa-barcode ${iconSizeClasses[iconSize]} text-[#66B8B1] dark:text-[#5DBDC0]`}></i>
      {withText && (
        <span className={`${textSizeClasses[textSize]} font-semibold text-gray-800 dark:text-gray-100 icon-exempt`}>
          Barcode Cafe
        </span>
      )}
    </div>
  );
  
  // If linkTo is provided, wrap the logo in a Link
  if (linkTo) {
    return <Link href={linkTo}>{logoContent}</Link>;
  }
  
  // Otherwise, just return the logo
  return logoContent;
} 