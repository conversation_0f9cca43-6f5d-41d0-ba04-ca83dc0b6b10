"use client";

import React, { useState } from 'react';
import DefaultAvatarSvg from '@/assets/avatars/default-avatar.svg';

interface UserAvatarProps {
  src?: string | null;
  alt?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

/**
 * UserAvatar component that displays a user's avatar image with a fallback to the default avatar.
 * 
 * @param src - Optional URL to the user's avatar image
 * @param alt - Alternative text for the avatar image
 * @param size - Size variant of the avatar (sm, md, lg, xl)
 * @param className - Additional CSS classes
 */
export default function UserAvatar({ 
  src, 
  alt = "User profile", 
  size = "md",
  className = ""
}: UserAvatarProps) {
  const [imageError, setImageError] = useState(false);
  
  // Handle image loading error
  const handleError = () => {
    setImageError(true);
  };
  
  // Determine size class
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-10 h-10",
    lg: "w-20 h-20",
    xl: "w-24 h-24",
  };
  
  const sizeClass = sizeClasses[size];
  
  // If there's a valid image source and no error, display the image
  if (src && !imageError) {
    return (
      <div className={`${sizeClass} rounded-full overflow-hidden ${className}`}>
        <img 
          src={src}
          alt={alt}
          className="w-full h-full object-cover"
          onError={handleError}
        />
      </div>
    );
  }
  
  // Otherwise, display the default SVG avatar
  return (
    <div className={`${sizeClass} rounded-full overflow-hidden ${className}`}>
      <DefaultAvatarSvg className="w-full h-full" />
    </div>
  );
} 