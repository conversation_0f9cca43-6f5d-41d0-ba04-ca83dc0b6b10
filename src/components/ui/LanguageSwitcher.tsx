"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useLocale } from '@/contexts/LocaleContext';

export default function LanguageSwitcher() {
  const { locale, setLocale } = useLocale();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLanguageChange = (newLocale: 'en' | 'ar') => {
    setLocale(newLocale);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button 
        onClick={() => setIsOpen(!isOpen)} 
        className="flex items-center justify-center p-2.5 rounded-full dark:bg-[#242832] bg-white shadow-sm hover:shadow-md transition-all lang-switcher"
        aria-label="Select language"
        title="Select language"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <div className="relative overflow-hidden flex items-center justify-center">
          {/* Current language flag */}
          <div className="flex items-center justify-center h-6 w-6">
            <span className="text-xl" role="img" aria-label={locale === 'en' ? 'English' : 'Arabic'}>
              {locale === 'en' ? '🇬🇧' : '🇸🇦'}
            </span>
          </div>
        </div>
      </button>
      
      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute mt-2 w-40 bg-white dark:bg-[#242832] rounded-xl shadow-lg overflow-hidden z-50 py-1 lang-dropdown-menu border border-gray-200 dark:border-gray-700">
          <button
            className={`w-full text-left px-4 py-3 text-sm flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-[#1d2127] ${locale === 'en' ? 'bg-gray-50 dark:bg-[#1d2127]' : ''}`}
            onClick={() => handleLanguageChange('en')}
          >
            <span className="text-xl" role="img" aria-label="English">🇬🇧</span>
            <span className="dark:text-gray-100">English</span>
            {locale === 'en' && <i className="fa-solid fa-check ml-auto text-green-500"></i>}
          </button>
          <button
            className={`w-full text-left px-4 py-3 text-sm flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-[#1d2127] ${locale === 'ar' ? 'bg-gray-50 dark:bg-[#1d2127]' : ''}`}
            onClick={() => handleLanguageChange('ar')}
          >
            <span className="text-xl" role="img" aria-label="Arabic">🇸🇦</span>
            <span className="dark:text-gray-100">العربية</span>
            {locale === 'ar' && <i className="fa-solid fa-check ml-auto text-green-500"></i>}
          </button>
        </div>
      )}
    </div>
  );
} 