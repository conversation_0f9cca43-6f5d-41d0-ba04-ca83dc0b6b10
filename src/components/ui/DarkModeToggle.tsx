"use client";

import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';

export default function DarkModeToggle() {
  const { toggleTheme, isDark } = useTheme();

  return (
    <button 
      onClick={toggleTheme} 
      className="flex items-center justify-center p-2 rounded-full bg-white dark:bg-[#1d2127] shadow-sm hover:shadow-md transition-all"
      aria-label={isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
      title={isDark ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
    >
      <div className="relative h-6 w-6 overflow-hidden flex items-center justify-center">
        {isDark ? (
          // Sun icon for light mode
          <i className="fa-solid fa-sun text-yellow-400"></i>
        ) : (
          // Moon icon for dark mode
          <i className="fa-solid fa-moon text-indigo-600"></i>
        )}
      </div>
    </button>
  );
} 