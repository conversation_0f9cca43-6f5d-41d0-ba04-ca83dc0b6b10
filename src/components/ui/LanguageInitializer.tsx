"use client";

import { useEffect } from 'react';
import { useLocale } from '@/contexts/LocaleContext';

export default function LanguageInitializer() {
  const { locale } = useLocale();
  
  // This effect runs once on the client side after hydration
  useEffect(() => {
    // Set up the document attributes based on the saved locale
    const htmlElement = document.documentElement;
    
    if (locale === 'ar') {
      htmlElement.dir = 'rtl';
      htmlElement.lang = 'ar';
      htmlElement.classList.add('font-arabic');
      document.body.classList.add('font-arabic');
    } else {
      htmlElement.dir = 'ltr';
      htmlElement.lang = 'en';
      htmlElement.classList.remove('font-arabic');
      document.body.classList.remove('font-arabic');
    }
  }, [locale]);
  
  // This component doesn't render anything visible
  return null;
} 