"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";

export default function Sidebar() {
  const { t, isClient } = useLocale();
  const pathname = usePathname();
  
  const navigationLinks = [
    {
      name: isClient ? t('nav.dashboard') : 'Dashboard',
      href: '/customer/dashboard',
      icon: 'fa-solid fa-gauge-high'
    },
    {
      name: isClient ? t('nav.menu') : 'Menu',
      href: '/menu',
      icon: 'fa-solid fa-utensils'
    },
    {
      name: isClient ? t('nav.orderHistory') : 'Order History',
      href: '/customer/orders',
      icon: 'fa-solid fa-clock-rotate-left'
    },
    {
      name: isClient ? t('nav.giftCards') : 'Gift Cards',
      href: '/customer/gift-cards',
      icon: 'fa-solid fa-gift'
    },
    {
      name: isClient ? t('nav.addresses') : 'Addresses',
      href: '/customer/addresses',
      icon: 'fa-solid fa-location-dot'
    },
    {
      name: isClient ? t('nav.reviews') : 'Reviews',
      href: '/customer/reviews',
      icon: 'fa-solid fa-star'
    },
  ];
  
  return (
    <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
      <nav className="space-y-2">
        {navigationLinks.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                isActive 
                  ? 'text-[#56999B] dark:text-[#5DBDC0] bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20' 
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-[#242832]'
              }`}
            >
              <i className={`${item.icon} ${isActive ? 'text-[#56999B] dark:text-[#5DBDC0]' : ''}`}></i>
              <span className={isActive ? 'font-medium' : ''}>{item.name}</span>
            </Link>
          );
        })}
      </nav>
    </div>
  );
} 