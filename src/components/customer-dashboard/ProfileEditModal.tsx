"use client";

import React, { useState, useEffect } from 'react';
import { useLocale } from "@/contexts/LocaleContext";
import UserAvatar from "@/components/ui/UserAvatar";
import { UserProfile } from '@/types/models';
import { updateUserProfile } from '@/lib/firebase/firestore';
import { useAuth } from '@/contexts/AuthContext';

interface ProfileEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  userAvatarSrc?: string | null;
  userProfile: UserProfile | null;
}

export default function ProfileEditModal({ isOpen, onClose, userAvatarSrc, userProfile }: ProfileEditModalProps) {
  const { t, dir, isClient } = useLocale();
  const { user } = useAuth();
  
  // Form state
  const [formData, setFormData] = useState({
    displayName: '',
    email: '',
    phoneNumber: '',
    preferences: {
      language: 'en',
      darkMode: false,
      emailNotifications: true,
      pushNotifications: false
    }
  });
  
  // Split name into first and last name for form
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  
  // Loading state
  const [isLoading, setIsLoading] = useState(false);
  
  // Initialize form with user data
  useEffect(() => {
    if (userProfile) {
      // Split display name into first and last name
      const nameParts = userProfile.displayName?.split(' ') || ['', ''];
      
      setFirstName(nameParts[0] || '');
      setLastName(nameParts.slice(1).join(' ') || '');
      
      setFormData({
        displayName: userProfile.displayName || '',
        email: userProfile.email || '',
        phoneNumber: userProfile.phoneNumber || '',
        preferences: {
          language: userProfile.preferences?.language || 'en',
          darkMode: userProfile.preferences?.darkMode || false,
          emailNotifications: userProfile.preferences?.emailNotifications || true,
          pushNotifications: userProfile.preferences?.pushNotifications || false
        }
      });
    } else if (user) {
      // Initialize with Firebase user data if available
      const nameParts = user.displayName?.split(' ') || ['', ''];
      
      setFirstName(nameParts[0] || '');
      setLastName(nameParts.slice(1).join(' ') || '');
      
      setFormData({
        displayName: user.displayName || '',
        email: user.email || '',
        phoneNumber: user.phoneNumber || '',
        preferences: {
          language: 'en',
          darkMode: false,
          emailNotifications: true,
          pushNotifications: false
        }
      });
    }
  }, [userProfile, user]);
  
  // Update formData when first or last name changes
  useEffect(() => {
    const fullName = `${firstName} ${lastName}`.trim();
    setFormData(prev => ({ ...prev, displayName: fullName }));
  }, [firstName, lastName]);
  
  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [name]: checked
      }
    }));
  };
  
  // Save profile changes
  const handleSaveChanges = async () => {
    if (!user?.uid) return;
    
    setIsLoading(true);
    try {
      await updateUserProfile(user.uid, {
        displayName: formData.displayName,
        phoneNumber: formData.phoneNumber,
        preferences: formData.preferences
      });
      
      onClose();
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Add RTL-aware classes
  const rtlAwareClasses = {
    textAlign: dir === 'rtl' ? 'text-right' : 'text-left',
    labelClass: `block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 ${dir === 'rtl' ? 'text-right' : 'text-left'}`,
    contentContainer: `${dir === 'rtl' ? 'rtl:text-right' : ''}`,
  };
  
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-lg w-[600px] max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold dark:text-gray-100">
              {isClient ? t('customer.profileEdit.title') : 'Edit Profile'}
            </h2>
            <button 
              className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
              onClick={onClose}
            >
              <i className="fa-solid fa-xmark text-xl"></i>
            </button>
          </div>
        </div>

        <div className={`p-6 space-y-6 ${rtlAwareClasses.contentContainer}`}>
          {/* Profile Picture Section */}
          <div id="profile-picture-section" className="flex items-center gap-6">
            <UserAvatar 
              src={userAvatarSrc}
              alt="Profile"
              size="xl"
            />
            <div>
              <button className="bg-[#83EAED] dark:bg-[#5DBDC0] text-white px-4 py-2 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0] mb-2">
                <i className="fa-solid fa-camera mr-2"></i>
                {isClient ? t('customer.profileEdit.profilePicture') : 'Change Photo'}
              </button>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isClient ? t('customer.profileEdit.maxFileSize') : 'Maximum file size: 2MB'}
              </p>
            </div>
          </div>

          {/* Personal Information */}
          <div id="personal-info-section" className="space-y-4">
            <h3 className="font-semibold text-lg dark:text-gray-100">
              {isClient ? t('customer.profileEdit.personalInfo') : 'Personal Information'}
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className={rtlAwareClasses.labelClass}>
                  {isClient ? t('customer.profileEdit.firstName') : 'First Name'}
                </label>
                <input 
                  type="text" 
                  name="firstName"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
                />
              </div>
              <div>
                <label className={rtlAwareClasses.labelClass}>
                  {isClient ? t('customer.profileEdit.lastName') : 'Last Name'}
                </label>
                <input 
                  type="text" 
                  name="lastName"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
                />
              </div>
            </div>
            <div>
              <label className={rtlAwareClasses.labelClass}>
                {isClient ? t('customer.profileEdit.email') : 'Email Address'}
              </label>
              <input 
                type="email"
                name="email"
                value={formData.email}
                disabled
                className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-700 dark:text-gray-400"
              />
              <p className="text-xs text-gray-500 mt-1">
                {isClient ? t('customer.profileEdit.emailNotEditable') : 'Email address cannot be changed'}
              </p>
            </div>
            <div>
              <label className={rtlAwareClasses.labelClass}>
                {isClient ? t('customer.profileEdit.phone') : 'Phone Number'}
              </label>
              <input 
                type="tel"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={handleInputChange}
                className="w-full px-4 py-2 border dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#83EAED] dark:focus:ring-[#5DBDC0] focus:border-[#83EAED] dark:focus:border-[#5DBDC0] dark:bg-[#242832] dark:text-gray-100"
              />
            </div>
          </div>

          {/* Preferences */}
          <div id="preferences-section" className="space-y-4">
            <h3 className="font-semibold text-lg dark:text-gray-100">
              {isClient ? t('customer.profileEdit.preferences') : 'Preferences'}
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-4 border dark:border-gray-600 rounded-lg dark:bg-[#242832]">
                <div>
                  <h4 className="font-medium dark:text-gray-100">
                    {isClient ? t('customer.profileEdit.emailNotifs') : 'Email Notifications'}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isClient ? t('customer.profileEdit.emailNotifsDesc') : 'Receive order updates and promotions'}
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox"
                    name="emailNotifications"
                    checked={formData.preferences.emailNotifications}
                    onChange={handleCheckboxChange}
                    className="sr-only peer" 
                  />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#83EAED] dark:peer-checked:bg-[#5DBDC0]"></div>
                </label>
              </div>
              <div className="flex items-center justify-between p-4 border dark:border-gray-600 rounded-lg dark:bg-[#242832]">
                <div>
                  <h4 className="font-medium dark:text-gray-100">
                    {isClient ? t('customer.profileEdit.smsNotifs') : 'SMS Notifications'}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isClient ? t('customer.profileEdit.smsNotifsDesc') : 'Get text messages for order status'}
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input 
                    type="checkbox"
                    name="pushNotifications"
                    checked={formData.preferences.pushNotifications}
                    onChange={handleCheckboxChange}
                    className="sr-only peer" 
                  />
                  <div className="w-11 h-6 bg-gray-200 dark:bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#83EAED] dark:peer-checked:bg-[#5DBDC0]"></div>
                </label>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6 border-t dark:border-gray-700 bg-gray-50 dark:bg-[#242832] flex justify-end gap-3">
          <button 
            className="px-6 py-2 border dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-[#1d2127] dark:text-gray-300"
            onClick={onClose}
            disabled={isLoading}
          >
            {isClient ? t('customer.profileEdit.cancel') : 'Cancel'}
          </button>
          <button 
            className="bg-[#56999B] dark:bg-[#5DBDC0] text-white px-6 py-2 rounded-lg hover:bg-[#56999B]/90 dark:hover:bg-[#4A9EA0] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            onClick={handleSaveChanges}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                {isClient ? t('customer.profileEdit.saving') : 'Saving...'}
              </>
            ) : (
              isClient ? t('customer.profileEdit.saveChanges') : 'Save Changes'
            )}
          </button>
        </div>
      </div>
    </div>
  );
} 