"use client";

import { useState, ReactNode } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import Sidebar from "./Sidebar";
import Header from "./Header";

interface DashboardLayoutProps {
  children: ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { dir } = useLocale();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  return (
    <div className="min-h-screen bg-[#F9FDFC] dark:bg-[#171c23]" dir={dir}>
      <Header toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
      
      <div className="container mx-auto px-4 py-8 flex flex-col md:flex-row gap-6">
        {/* Mobile sidebar */}
        <div className={`fixed inset-0 z-40 ${sidebarOpen ? 'block' : 'hidden'} md:hidden`}>
          {/* Overlay */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50" 
            onClick={() => setSidebarOpen(false)}
            aria-hidden="true"
          ></div>
          
          {/* Sidebar */}
          <div className={`fixed ${dir === 'rtl' ? 'right-0' : 'left-0'} inset-y-0 flex w-3/4 max-w-sm flex-col bg-[#F9FDFC] dark:bg-[#171c23] shadow-xl`}>
            <div className="h-screen p-4 overflow-y-auto">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold dark:text-white">Menu</h2>
                <button 
                  onClick={() => setSidebarOpen(false)}
                  className="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <i className="fa-solid fa-xmark text-xl"></i>
                </button>
              </div>
              <Sidebar />
            </div>
          </div>
        </div>
        
        {/* Desktop sidebar */}
        <div className="hidden md:block w-64 flex-shrink-0">
          <Sidebar />
        </div>
        
        {/* Main content */}
        <div className="flex-1 space-y-6">
          {children}
        </div>
      </div>
    </div>
  );
} 