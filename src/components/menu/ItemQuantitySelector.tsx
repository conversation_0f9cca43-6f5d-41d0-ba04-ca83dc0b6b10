'use client';

import { useState } from 'react';
import { useLocale } from "@/contexts/LocaleContext";

interface ItemQuantitySelectorProps {
  initialQuantity?: number;
  minQuantity?: number;
  maxQuantity?: number;
  onChange?: (quantity: number) => void;
}

export default function ItemQuantitySelector({
  initialQuantity = 1,
  minQuantity = 1,
  maxQuantity = 99,
  onChange
}: ItemQuantitySelectorProps) {
  const { t, isClient } = useLocale();
  const [quantity, setQuantity] = useState(initialQuantity);
  
  const handleIncrement = () => {
    if (quantity < maxQuantity) {
      const newQuantity = quantity + 1;
      setQuantity(newQuantity);
      onChange?.(newQuantity);
    }
  };
  
  const handleDecrement = () => {
    if (quantity > minQuantity) {
      const newQuantity = quantity - 1;
      setQuantity(newQuantity);
      onChange?.(newQuantity);
    }
  };
  
  return (
    <div className="flex items-center p-3 bg-white dark:bg-[#322a22] rounded-lg justify-between">
      <span className="text-[#703f23] dark:text-[#e09a62] font-medium">
        {isClient ? t('menu.quantity') : 'Quantity'}
      </span>
      
      <div className="flex items-center">
        <button 
          onClick={handleDecrement}
          className={`w-8 h-8 rounded-full flex items-center justify-center border font-medium text-lg 
            ${quantity <= minQuantity 
              ? 'border-gray-200 text-gray-300 dark:border-gray-700 dark:text-gray-600 cursor-not-allowed' 
              : 'border-[#c27845] text-[#c27845] dark:border-[#d27d46] dark:text-[#d27d46] hover:bg-[#c27845]/10 dark:hover:bg-[#d27d46]/10'
            }`}
          disabled={quantity <= minQuantity}
        >
          -
        </button>
        
        <span className="mx-3 w-6 text-center text-[#703f23] dark:text-[#e09a62] font-semibold">
          {quantity}
        </span>
        
        <button 
          onClick={handleIncrement}
          className={`w-8 h-8 rounded-full flex items-center justify-center border font-medium text-lg
            ${quantity >= maxQuantity 
              ? 'border-gray-200 text-gray-300 dark:border-gray-700 dark:text-gray-600 cursor-not-allowed' 
              : 'border-[#c27845] text-[#c27845] dark:border-[#d27d46] dark:text-[#d27d46] hover:bg-[#c27845]/10 dark:hover:bg-[#d27d46]/10'
            }`}
          disabled={quantity >= maxQuantity}
        >
          +
        </button>
      </div>
    </div>
  );
} 