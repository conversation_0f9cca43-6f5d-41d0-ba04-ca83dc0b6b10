'use client';

import { useState } from 'react';
import { useCart } from '@/contexts/CartContext';
import DeliveryOptions from '@/components/checkout/DeliveryOptions';
import { useLocale } from '@/contexts/LocaleContext';
import { useAuth } from '@/contexts/AuthContext';
import { Sheet, SheetContent, SheetTitle } from '@/components/ui/sheet';
import { useRouter } from 'next/navigation';
import { DeliveryType } from '@/types/delivery';

export default function CartButton() {
  const { t, isClient, locale } = useLocale();
  const { cartItems, cartTotal, cartItemsCount, updateQuantity, removeFromCart, placeOrder, deliveryOption, setDeliveryOption, totalWithDelivery, clearCart } = useCart();
  const { user } = useAuth();
  const router = useRouter();
  const isRTL = locale === 'ar';
  
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);
  const [checkoutStep, setCheckoutStep] = useState<'cart' | 'delivery'>('cart');

  const handleProceedToDelivery = () => {
    setCheckoutStep('delivery');
  };

  const handleBackToCart = () => {
    setCheckoutStep('cart');
  };

  const handlePlaceOrder = async () => {
    if (!user) {
      // Close cart sheet and redirect to login
      setIsCartOpen(false);
      router.push('/signin');
      return;
    }
    
    setIsPlacingOrder(true);
    try {
      await placeOrder();
      setIsCartOpen(false);
    } catch (error) {
      console.error('Error placing order:', error);
    } finally {
      setIsPlacingOrder(false);
    }
  };
  
  if (cartItemsCount === 0) {
    return null;
  }
  
  return (
    <>
      <button 
        onClick={() => setIsCartOpen(true)}
        className="fixed bottom-6 right-6 w-16 h-16 rounded-full bg-[#c27845] dark:bg-[#d27d46] text-white flex items-center justify-center shadow-xl hover:bg-[#b06735] dark:hover:bg-[#c16e37] transition-all hover:scale-105 z-50"
        aria-label={isClient ? t('menu.viewCart') : 'View Cart'}
      >
        <i className="fa-solid fa-cart-shopping text-xl"></i>
        {cartItemsCount > 0 && (
          <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-7 h-7 flex items-center justify-center shadow-md">
            {cartItemsCount}
          </div>
        )}
      </button>
      
      <Sheet open={isCartOpen} onOpenChange={setIsCartOpen}>
        <SheetContent 
          side={isRTL ? "left" : "right"}
          className="bg-white dark:bg-[#392e23] p-0 overflow-y-auto max-w-md w-full"
        >
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-[#dac6ae] dark:border-[#392e23]">
              <SheetTitle className="text-2xl font-bold text-[#703f23] dark:text-[#e09a62] flex items-center">
                <i className="fa-solid fa-cart-shopping mr-3 text-[#c27845] dark:text-[#d27d46]"></i>
                {isClient ? t('menu.cart') : 'Cart'}
              </SheetTitle>
            </div>
            
            {cartItems.length === 0 ? (
              <div className="flex-grow flex flex-col items-center justify-center p-8">
                <div className="bg-[#f8f5e9] dark:bg-[#2c3436] w-24 h-24 rounded-full flex items-center justify-center mb-6">
                  <i className="fa-solid fa-cart-shopping text-[#c27845]/50 dark:text-[#d27d46]/50 text-4xl"></i>
                </div>
                <h3 className="text-[#703f23] dark:text-[#e09a62] font-semibold text-xl mb-2">
                  {isClient ? t('menu.emptyCart') : 'Your cart is empty'}
                </h3>
                <p className="text-[#94795e] dark:text-[#b49678] text-center max-w-xs">
                  {isClient ? t('menu.emptyCart') : 'Add some delicious items to your cart to get started'}
                </p>
              </div>
            ) : (
              <>
                <div className="flex-grow overflow-auto">
                  <div className="p-6">
                    <div className="space-y-4">
                      {checkoutStep === 'cart' && (
                        <>
                          {cartItems.map(item => (
                            <div key={item.id} className="flex items-center gap-4 pb-4 border-b border-[#dac6ae] dark:border-[#392e23] last:border-0">
                              <div className="flex-shrink-0 w-16 h-16 bg-[#f8f5e9] dark:bg-[#2c3436] rounded-lg overflow-hidden">
                                {item.image && (
                                  <img 
                                    src={item.image} 
                                    alt={item.title} 
                                    className="w-full h-full object-cover"
                                  />
                                )}
                              </div>
                              
                              <div className="flex-grow">
                                <div className="flex justify-between">
                                  <h3 className="font-semibold text-[#703f23] dark:text-[#e09a62]">
                                    {item.title}
                                  </h3>
                                  <button 
                                    onClick={() => removeFromCart(item.id)}
                                    className="text-red-500 dark:text-red-400 p-1 hover:text-red-600 dark:hover:text-red-300 transition-colors"
                                    aria-label={isClient ? t('common.remove') : 'Remove'}
                                  >
                                    <i className="fa-solid fa-times"></i>
                                  </button>
                                </div>
                                
                                <div className="flex justify-between items-center mt-2">
                                  <div className="flex items-center gap-2">
                                    <button 
                                      onClick={() => updateQuantity(item.id, Math.max(1, item.quantity - 1))}
                                      className="w-8 h-8 rounded-full bg-[#f8f5e9] dark:bg-[#2c3436] flex items-center justify-center text-[#703f23] dark:text-[#e09a62]"
                                      aria-label="Decrease quantity"
                                    >
                                      <i className="fa-solid fa-minus text-xs"></i>
                                    </button>
                                    
                                    <span className="font-medium text-[#703f23] dark:text-[#e09a62] min-w-[1.5rem] text-center">
                                      {item.quantity}
                                    </span>
                                    
                                    <button 
                                      onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                      className="w-8 h-8 rounded-full bg-[#f8f5e9] dark:bg-[#2c3436] flex items-center justify-center text-[#703f23] dark:text-[#e09a62]"
                                      aria-label="Increase quantity"
                                    >
                                      <i className="fa-solid fa-plus text-xs"></i>
                                    </button>
                                  </div>
                                  
                                  <div className="font-medium text-[#703f23] dark:text-[#e09a62]">
                                    {isClient ? t('common.currency') : 'SAR'} {(item.price * item.quantity).toFixed(2)}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="border-t border-[#dac6ae] dark:border-[#392e23]">
                  <div className="p-6">
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-[#94795e] dark:text-[#b49678]">
                        {isClient ? t('menu.subtotal') : 'Subtotal'}
                      </span>
                      <span className="font-medium text-[#703f23] dark:text-[#e09a62]">
                        {isClient ? t('common.currency') : 'SAR'} {cartTotal.toFixed(2)}
                      </span>
                    </div>
                    
                    {deliveryOption?.fee && deliveryOption.fee > 0 && (
                      <div className="flex justify-between items-center mb-4">
                        <span className="text-[#94795e] dark:text-[#b49678]">
                          {isClient ? t('checkout.deliveryFee') : 'Delivery Fee'}
                        </span>
                        <span className="font-medium text-[#703f23] dark:text-[#e09a62]">
                          {isClient ? t('common.currency') : 'SAR'} {deliveryOption.fee.toFixed(2)}
                        </span>
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center mb-6 pt-2 border-t border-[#dac6ae] dark:border-[#392e23]">
                      <span className="font-medium text-[#703f23] dark:text-[#e09a62]">
                        {isClient ? t('menu.total') : 'Total'}
                      </span>
                      <span className="font-bold text-lg text-[#703f23] dark:text-[#e09a62]">
                        {isClient ? t('common.currency') : 'SAR'} {totalWithDelivery.toFixed(2)}
                      </span>
                    </div>
                    
                    <div className="flex flex-col gap-3">
                      {checkoutStep === 'cart' && (
                        <button
                          onClick={handleProceedToDelivery}
                          className="w-full bg-[#c27845] dark:bg-[#d27d46] text-white py-3 rounded-full font-medium hover:bg-[#b06735] dark:hover:bg-[#c16e37] transition-colors flex items-center justify-center"
                        >
                          <i className="fa-solid fa-arrow-right mr-2"></i>
                          {isClient ? t('checkout.proceedToDelivery') : 'Proceed to Delivery Options'}
                        </button>
                      )}
                      
                      {checkoutStep === 'delivery' && (
                        <div className="space-y-4">
                          <DeliveryOptions
                            onDeliveryOptionSelected={setDeliveryOption}
                            selectedOption={deliveryOption || undefined}
                          />

                          {deliveryOption && (
                            <div className="p-4 bg-[#f8f5e9] dark:bg-[#2c3436] rounded-lg border border-[#dac6ae] dark:border-[#392e23]">
                              <h3 className="font-medium text-[#703f23] dark:text-[#e09a62] mb-2">
                                {isClient ? t('checkout.orderSummary') : 'Order Summary'}
                              </h3>

                              <div className="text-sm text-[#94795e] dark:text-[#b49678] space-y-1">
                                {deliveryOption?.type === DeliveryType.TABLE && deliveryOption.tableZone && deliveryOption.tableNumber && (
                                  <>
                                    <p><strong>{isClient ? t('checkout.tableZone') : 'Table Zone'}:</strong> {deliveryOption.tableZone.name}</p>
                                    <p><strong>{isClient ? t('checkout.tableNumber') : 'Table Number'}:</strong> {deliveryOption.tableNumber}</p>
                                  </>
                                )}

                                {deliveryOption?.type === DeliveryType.PICK_UP && (
                                  <p><strong>{isClient ? t('checkout.deliveryType') : 'Delivery Type'}:</strong> {isClient ? t('checkout.pickUpFromCounter') : 'Pick up from counter'}</p>
                                )}

                                {deliveryOption?.type === DeliveryType.DELIVERY && deliveryOption.deliveryZone && (
                                  <>
                                    <p><strong>{isClient ? t('checkout.deliveryZone') : 'Delivery Zone'}:</strong> {deliveryOption.deliveryZone.name}</p>
                                    <p><strong>{isClient ? t('checkout.deliveryAddress') : 'Delivery Address'}:</strong> {deliveryOption.deliveryAddress}</p>
                                    {deliveryOption.fee && deliveryOption.fee > 0 && (
                                      <p><strong>{isClient ? t('checkout.deliveryFee') : 'Delivery Fee'}:</strong> {isClient ? t('common.currency') : 'SAR'} {deliveryOption.fee.toFixed(2)}</p>
                                    )}
                                  </>
                                )}

                                <div className="pt-2 mt-2 border-t border-[#dac6ae] dark:border-[#392e23]">
                                  <p><strong>{isClient ? t('checkout.paymentMethod') : 'Payment Method'}:</strong> {isClient ? t('checkout.cashOnDelivery') : 'Cash on Delivery/Pickup'}</p>
                                </div>
                              </div>
                            </div>
                          )}

                          <div className="flex gap-2 mt-4">
                            <button
                              onClick={handleBackToCart}
                              className="flex-1 border border-[#c27845] dark:border-[#d27d46] text-[#c27845] dark:text-[#d27d46] py-3 rounded-full font-medium hover:bg-[#c27845]/10 dark:hover:bg-[#d27d46]/10 transition-colors flex items-center justify-center"
                            >
                              <i className="fa-solid fa-arrow-left mr-2"></i>
                              {isClient ? t('common.back') : 'Back'}
                            </button>
                            
                            <button
                              onClick={handlePlaceOrder}
                              disabled={
                                isPlacingOrder ||
                                (deliveryOption?.type === DeliveryType.TABLE && !deliveryOption?.tableNumber) ||
                                (deliveryOption?.type === DeliveryType.DELIVERY && (!deliveryOption?.deliveryZone || !deliveryOption?.deliveryAddress))
                              }
                              className="flex-1 bg-[#c27845] dark:bg-[#d27d46] text-white py-3 rounded-full font-medium hover:bg-[#b06735] dark:hover:bg-[#c16e37] transition-colors flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isPlacingOrder ? (
                                <>
                                  <span className="mr-2">{isClient ? t('menu.placingOrder') : 'Placing Order...'}</span>
                                  <i className="fa-solid fa-spinner fa-spin"></i>
                                </>
                              ) : (
                                <>
                                  <i className="fa-solid fa-check-circle mr-2"></i>
                                  {isClient ? t('checkout.confirmOrder') : 'Confirm Order'}
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      )}

                      
                      <button
                        onClick={() => clearCart()}
                        className="w-full py-3 text-[#703f23] dark:text-[#e09a62] font-medium hover:underline transition-all flex items-center justify-center"
                      >
                        <i className="fa-solid fa-trash-can mr-2 text-sm"></i>
                        {isClient ? t('menu.clearCart') : 'Clear Cart'}
                      </button>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
}
