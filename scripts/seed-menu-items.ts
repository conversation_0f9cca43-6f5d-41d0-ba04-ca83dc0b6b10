/// <reference types="node" />
/**
 * Seed Script for Menu Items
 * 
 * This script inserts sample menu items into Firestore for testing purposes.
 * It uses batch operations for efficiency and checks for duplicates before insertion.
 * 
 * Usage:
 * 1. Run with default admin user: npm run seed:menu-items
 * 2. Run with specific user ID: npm run seed:menu-items -- --userId=YOUR_USER_ID
 */

import * as dotenv from 'dotenv';
// Load from .env.local file
dotenv.config({ path: '.env.local' });

import { initializeApp } from 'firebase/app';
import { 
  getFirestore, 
  collection, 
  query, 
  where, 
  getDocs, 
  writeBatch, 
  doc, 
  serverTimestamp 
} from 'firebase/firestore';
import { StockStatus } from '../src/types/models';
import { randomUUID } from 'crypto';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Log Firebase config for debugging (redacting sensitive info)
console.log('Firebase Config:', {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? '******' : 'undefined',
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID ? '******' : 'undefined',
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID ? '******' : 'undefined'
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Constants
const CATEGORIES_COLLECTION = 'categories';
const MENU_ITEMS_COLLECTION = 'menuItems';
const DEFAULT_ADMIN_ID = '4riKYvfThiM4muMIgb7I0wUL2Jt2'; // Replace with actual default admin ID if needed

// Get user ID from command line arguments or use default
function getUserId(): string {
  const userIdArg = process.argv.find(arg => arg.startsWith('--userId='));
  if (userIdArg) {
    return userIdArg.split('=')[1];
  }
  return DEFAULT_ADMIN_ID;
}

// Sample menu item data
const sampleMenuItemsData = [
  // Hot Beverages
  {
    title: 'Cappuccino',
    description: 'Espresso with a perfect balance of steamed milk and foam',
    price: 4.50,
    image: 'https://images.unsplash.com/photo-1572442388796-11668a67e53d?auto=format&fit=crop&w=800&q=80',
    prepTime: 5,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 100,
    isFeatured: true,
    isAvailableForDelivery: true,
    categoryName: 'Hot Beverages',
    categoryIcon: 'fa-mug-hot'
  },
  {
    title: 'Latte',
    description: 'Espresso with steamed milk and a light layer of foam',
    price: 4.25,
    image: 'https://images.unsplash.com/photo-1570968915860-54d5c301fa9f?auto=format&fit=crop&w=800&q=80',
    prepTime: 5,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 100,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Hot Beverages',
    categoryIcon: 'fa-mug-hot'
  },
  {
    title: 'Espresso',
    description: 'Pure, strong coffee shot with a rich crema layer',
    price: 3.00,
    image: 'https://images.unsplash.com/photo-1596952954288-16862d37405b?auto=format&fit=crop&w=800&q=80',
    prepTime: 3,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 100,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Hot Beverages',
    categoryIcon: 'fa-mug-hot'
  },
  {
    title: 'Mocha',
    description: 'Espresso with chocolate, steamed milk and whipped cream',
    price: 4.75,
    image: 'https://images.unsplash.com/photo-1578314675249-a6910f80cc39?auto=format&fit=crop&w=800&q=80',
    prepTime: 6,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 100,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Hot Beverages',
    categoryIcon: 'fa-mug-hot'
  },
  // Cold Beverages
  {
    title: 'Iced Coffee',
    description: 'Chilled coffee served over ice cubes',
    price: 3.75,
    image: 'https://images.unsplash.com/photo-1517701604599-bb29b565090c?auto=format&fit=crop&w=800&q=80',
    prepTime: 4,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 75,
    isFeatured: true,
    isAvailableForDelivery: true,
    categoryName: 'Cold Beverages',
    categoryIcon: 'fa-glass-water'
  },
  {
    title: 'Iced Latte',
    description: 'Espresso with cold milk served over ice',
    price: 4.50,
    image: 'https://images.unsplash.com/photo-1517701550927-30cf4ba1dba5?auto=format&fit=crop&w=800&q=80',
    prepTime: 5,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 75,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Cold Beverages',
    categoryIcon: 'fa-glass-water'
  },
  {
    title: 'Cold Brew',
    description: 'Smooth coffee slowly brewed with cold water for 12+ hours',
    price: 4.25,
    image: 'https://images.unsplash.com/photo-1583169192095-e4eeef1a7bc1?auto=format&fit=crop&w=800&q=80',
    prepTime: 3,
    stockStatus: StockStatus.LOW_STOCK,
    stockQuantity: 25,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Cold Beverages',
    categoryIcon: 'fa-glass-water'
  },
  {
    title: 'Fruit Smoothie',
    description: 'Blended fresh fruits with yogurt and honey',
    price: 5.50,
    image: 'https://images.unsplash.com/photo-1505252585461-04db1eb84625?auto=format&fit=crop&w=800&q=80',
    prepTime: 8,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 50,
    isFeatured: true,
    isAvailableForDelivery: true,
    categoryName: 'Cold Beverages',
    categoryIcon: 'fa-glass-water'
  },
  // Desserts
  {
    title: 'Chocolate Cake',
    description: 'Rich chocolate cake with a smooth ganache topping',
    price: 5.95,
    image: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?auto=format&fit=crop&w=800&q=80',
    prepTime: 0,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 20,
    isFeatured: true,
    isAvailableForDelivery: false,
    categoryName: 'Desserts',
    categoryIcon: 'fa-cake-candles'
  },
  {
    title: 'Cheesecake',
    description: 'Creamy New York style cheesecake with berry compote',
    price: 6.50,
    image: 'https://images.unsplash.com/photo-1567327613485-fbc7bf196198?auto=format&fit=crop&w=800&q=80',
    prepTime: 0,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 15,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Desserts',
    categoryIcon: 'fa-cake-candles'
  },
  {
    title: 'Tiramisu',
    description: 'Classic Italian dessert with coffee-soaked ladyfingers and mascarpone',
    price: 6.95,
    image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?auto=format&fit=crop&w=800&q=80',
    prepTime: 0,
    stockStatus: StockStatus.LOW_STOCK,
    stockQuantity: 8,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Desserts',
    categoryIcon: 'fa-cake-candles'
  },
  {
    title: 'Apple Pie',
    description: 'Warm apple pie with a flaky crust, served with ice cream',
    price: 5.50,
    image: 'https://images.unsplash.com/photo-1535920527002-b35e96722eb9?auto=format&fit=crop&w=800&q=80',
    prepTime: 0,
    stockStatus: StockStatus.OUT_OF_STOCK,
    stockQuantity: 0,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Desserts',
    categoryIcon: 'fa-cake-candles'
  },
  // Sandwiches & Snacks
  {
    title: 'Avocado Toast',
    description: 'Toasted artisan bread topped with fresh avocado, salt, and pepper',
    price: 7.50,
    image: 'https://images.unsplash.com/photo-1603046891744-76f2e0e1bd3d?auto=format&fit=crop&w=800&q=80',
    prepTime: 10,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 30,
    isFeatured: true,
    isAvailableForDelivery: true,
    categoryName: 'Sandwiches & Snacks',
    categoryIcon: 'fa-burger'
  },
  {
    title: 'Chicken Panini',
    description: 'Grilled chicken with mozzarella, pesto, and tomato on pressed ciabatta',
    price: 8.95,
    image: 'https://images.unsplash.com/photo-1550507992-eb63ffee0847?auto=format&fit=crop&w=800&q=80',
    prepTime: 12,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 25,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Sandwiches & Snacks',
    categoryIcon: 'fa-burger'
  },
  {
    title: 'Ham & Cheese Croissant',
    description: 'Buttery croissant filled with ham and melted Swiss cheese',
    price: 6.75,
    image: 'https://images.unsplash.com/photo-1608198093002-ad4e005484ec?auto=format&fit=crop&w=800&q=80',
    prepTime: 8,
    stockStatus: StockStatus.LOW_STOCK,
    stockQuantity: 12,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Sandwiches & Snacks',
    categoryIcon: 'fa-burger'
  },
  {
    title: 'Hummus Plate',
    description: 'Homemade hummus served with warm pita bread and fresh vegetables',
    price: 7.25,
    image: 'https://images.unsplash.com/photo-1540914124281-342587941389?auto=format&fit=crop&w=800&q=80',
    prepTime: 6,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 20,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Sandwiches & Snacks',
    categoryIcon: 'fa-burger'
  },
  // Breakfast
  {
    title: 'Classic Breakfast',
    description: 'Eggs, bacon, toast, and hash browns',
    price: 9.95,
    image: 'https://images.unsplash.com/photo-1533089860892-a7c6f10a081a?auto=format&fit=crop&w=800&q=80',
    prepTime: 15,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 40,
    isFeatured: true,
    isAvailableForDelivery: false,
    categoryName: 'Breakfast',
    categoryIcon: 'fa-egg'
  },
  {
    title: 'Pancake Stack',
    description: 'Fluffy pancakes with maple syrup and butter',
    price: 8.50,
    image: 'https://images.unsplash.com/photo-1565299543923-37dd37887442?auto=format&fit=crop&w=800&q=80',
    prepTime: 12,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 35,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Breakfast',
    categoryIcon: 'fa-egg'
  },
  {
    title: 'Fruit Parfait',
    description: 'Layers of yogurt, granola, and fresh seasonal fruits',
    price: 6.95,
    image: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?auto=format&fit=crop&w=800&q=80',
    prepTime: 5,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 30,
    isFeatured: false,
    isAvailableForDelivery: true,
    categoryName: 'Breakfast',
    categoryIcon: 'fa-egg'
  },
  {
    title: 'Vegetable Omelette',
    description: 'Three-egg omelette with bell peppers, onions, tomatoes, and cheese',
    price: 8.95,
    image: 'https://images.unsplash.com/photo-1510693206972-df098062cb71?auto=format&fit=crop&w=800&q=80',
    prepTime: 14,
    stockStatus: StockStatus.IN_STOCK,
    stockQuantity: 25,
    isFeatured: false,
    isAvailableForDelivery: false,
    categoryName: 'Breakfast',
    categoryIcon: 'fa-egg'
  }
];

/**
 * Seed menu items and categories
 */
async function seedMenuItems() {
  try {
    const userId = getUserId();
    console.log(`🌱 Starting seed process for user: ${userId}`);

    // Step 1: Get existing categories to check for duplicates and map them
    const categoryMap: Record<string, { id: string, count: number }> = {};
    const categoryQuery = query(
      collection(db, CATEGORIES_COLLECTION),
      where('userId', '==', userId)
    );
    
    const existingCategories = await getDocs(categoryQuery);
    existingCategories.forEach(doc => {
      const data = doc.data();
      categoryMap[data.name] = { id: doc.id, count: data.itemCount || 0 };
    });
    
    console.log(`Found ${existingCategories.size} existing categories`);

    // Step 2: Get existing menu items to avoid duplicates
    const menuItemsQuery = query(
      collection(db, MENU_ITEMS_COLLECTION),
      where('userId', '==', userId)
    );
    
    const existingMenuItems = await getDocs(menuItemsQuery);
    const existingMenuItemTitles = new Set<string>();
    existingMenuItems.forEach(doc => {
      existingMenuItemTitles.add(doc.data().title);
    });
    
    console.log(`Found ${existingMenuItems.size} existing menu items`);

    // Step 3: Prepare the batch operations
    const batch = writeBatch(db);
    let newItemsCount = 0;
    let newCategoriesCount = 0;
    const categoryItemCounts: Record<string, number> = {};

    // Step 4: Process each sample menu item
    for (const item of sampleMenuItemsData) {
      // Skip if item with same title already exists
      if (existingMenuItemTitles.has(item.title)) {
        console.log(`Skipping existing item: ${item.title}`);
        continue;
      }

      let categoryId: string;
      
      // Check if category exists, if not create it
      if (categoryMap[item.categoryName]) {
        categoryId = categoryMap[item.categoryName].id;
        categoryItemCounts[categoryId] = (categoryItemCounts[categoryId] || 0) + 1;
      } else {
        // Create a new category
        categoryId = randomUUID();
        const newCategory = {
          userId,
          name: item.categoryName,
          icon: item.categoryIcon,
          itemCount: 1,
          isActive: true,
          isVisible: true,
          isFeatured: false,
          displayOrder: Object.keys(categoryMap).length + 1,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };
        
        batch.set(doc(db, CATEGORIES_COLLECTION, categoryId), newCategory);
        categoryMap[item.categoryName] = { id: categoryId, count: 1 };
        categoryItemCounts[categoryId] = 1;
        newCategoriesCount++;
      }

      // Create the menu item
      const menuItemId = randomUUID();
      const menuItem = {
        userId,
        title: item.title,
        description: item.description,
        price: item.price,
        categoryId,
        image: item.image,
        stockStatus: item.stockStatus,
        stockQuantity: item.stockQuantity,
        prepTime: item.prepTime,
        isActive: true,
        isFeatured: item.isFeatured,
        isAvailableForDelivery: item.isAvailableForDelivery,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      batch.set(doc(db, MENU_ITEMS_COLLECTION, menuItemId), menuItem);
      newItemsCount++;
    }

    // Step 5: Update category item counts for existing categories
    for (const [categoryId, count] of Object.entries(categoryItemCounts)) {
      // Only update if it's an existing category
      if (Object.values(categoryMap).some(cat => cat.id === categoryId)) {
        const categoryRef = doc(db, CATEGORIES_COLLECTION, categoryId);
        const existingCount = Object.values(categoryMap).find(cat => cat.id === categoryId)?.count || 0;
        batch.update(categoryRef, { 
          itemCount: existingCount + count,
          updatedAt: serverTimestamp()
        });
      }
    }

    // Step 6: Commit the batch
    if (newItemsCount > 0 || newCategoriesCount > 0) {
      await batch.commit();
      console.log(`✅ Seed completed successfully!`);
      console.log(`📊 Stats:`);
      console.log(`   - Added ${newItemsCount} new menu items`);
      console.log(`   - Added ${newCategoriesCount} new categories`);
    } else {
      console.log(`ℹ️ No new items to add. All sample data already exists.`);
    }

  } catch (error) {
    console.error('❌ Error seeding menu items:', error);
    process.exit(1);
  }
}

// Run the seed function
seedMenuItems(); 