# Menu Items Seed Script

This script populates your Firestore database with sample menu items and categories for the Barcode Cafe app.

## Features

- Creates ~20 sample menu items across multiple categories
- Automatically creates any missing categories
- Uses Firebase batch operations for efficiency
- Prevents duplicates if re-run
- Updates item counts for existing categories
- Randomizes categories across items

## Prerequisites

1. Make sure you have a Firebase project set up
2. Configure your `.env.local` file with the necessary Firebase credentials:
   ```
   NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_auth_domain
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
   NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
   ```

## Usage

### Running with Default Admin User

```bash
npm run seed:menu-items
```

This will use the default admin user ID specified in the script.

### Running with Specific User ID

```bash
npm run seed:menu-items -- --userId=your_user_id
```

Replace `your_user_id` with the actual user ID you want to associate the menu items with.

## What Gets Created

The script creates:

1. **Menu Items**: Approx. 20 sample items with details like title, description, price, etc.
2. **Categories**: The following categories (if they don't already exist):
   - Hot Beverages
   - Cold Beverages
   - Desserts
   - Sandwiches & Snacks
   - Breakfast

## Duplicate Prevention

If you run the script multiple times:
- Menu items with the same title will be skipped
- New items will be added to existing categories
- Category item counts will be updated

## Customization

To modify the sample data:

1. Open `scripts/seed-menu-items.ts`
2. Edit the `sampleMenuItemsData` array to add/remove items or change their properties
3. Run the script again

## Troubleshooting

- **Error: Firebase App not initialized**: Make sure your Firebase credentials are correct in `.env.local`
- **No items added**: Check if all sample items already exist in your database
- **Permission issues**: Verify that the user ID has proper permissions in your Firebase security rules 