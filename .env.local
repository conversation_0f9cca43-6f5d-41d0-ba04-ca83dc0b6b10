# Firebase Client Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBgZ9UTZUfsIxwxIbmAMLspfAwacNvpf6c
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=barcode-qr-menu.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=barcode-qr-menu
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=barcode-qr-menu.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:de23fc4a9804e47300de9b
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-Y89R9F99ZV

# Base URL (required for auth actions)
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# Firebase Admin SDK Configuration
FIREBASE_SERVICE_ACCOUNT_KEY='{"type":"service_account","project_id":"barcode-qr-menu","private_key_id":"3499e75df953ada84fa154cef595158c9b0427c9","private_key":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","client_email":"<EMAIL>","client_id":"118203211214956999146","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40barcode-qr-menu.iam.gserviceaccount.com","universe_domain":"googleapis.com"}'

ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin123!
ADMIN_NAME='Admin User'

FIREBASE_DATABASE_URL=https://barcode-qr-menu.firebaseio.com

# Email Configuration (if using custom verification emails)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=password
EMAIL_FROM="BarcodeCafe" <<EMAIL>>