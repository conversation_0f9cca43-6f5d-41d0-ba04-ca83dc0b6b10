import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DeliveryZoneList from '../src/components/admin-dashboard/delivery-zones/DeliveryZoneList';
import { DeliveryZone, DeliveryZoneType } from '@/types/models';
import * as firestoreModule from '@/lib/firebase/firestore';

// Mock the firestore module
jest.mock('@/lib/firebase/firestore', () => ({
  deleteDeliveryZone: jest.fn().mockResolvedValue(undefined),
}));

// Mock the locale context
jest.mock('@/contexts/LocaleContext', () => ({
  useLocale: () => ({
    t: (key: string) => key, // Return the key as the translation
    isClient: true,
  }),
}));

// Mock the auth context
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { uid: 'test-user-id' },
  }),
}));

describe('DeliveryZoneList Component', () => {
  const mockZones: DeliveryZone[] = [
    {
      id: 'zone1',
      userId: 'test-user-id',
      name: 'Test Zone 1',
      type: DeliveryZoneType.PICK_UP,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 'zone2',
      userId: 'test-user-id',
      name: 'Test Zone 2',
      type: DeliveryZoneType.DELIVERY,
      isActive: true,
      deliveryFee: 5,
      minOrderAmount: 20,
      radius: 10,
      estimatedDeliveryTime: 30,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 'zone3',
      userId: 'test-user-id',
      name: 'Test Zone 3',
      type: DeliveryZoneType.IN_HOUSE_TABLES,
      isActive: false,
      tableNumbers: ['A1', 'A2', 'B1'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  const mockProps = {
    zones: mockZones,
    loading: false,
    onEdit: jest.fn(),
    onDelete: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state correctly', () => {
    render(<DeliveryZoneList {...mockProps} loading={true} />);
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  test('renders empty state when no zones are available', () => {
    render(<DeliveryZoneList {...mockProps} zones={[]} />);
    expect(screen.getByText('admin.noZonesMessage')).toBeInTheDocument();
  });

  test('renders zone list correctly', () => {
    render(<DeliveryZoneList {...mockProps} />);
    
    // Check if all zones are rendered
    expect(screen.getByText('Test Zone 1')).toBeInTheDocument();
    expect(screen.getByText('Test Zone 2')).toBeInTheDocument();
    expect(screen.getByText('Test Zone 3')).toBeInTheDocument();
    
    // Check if zone types are displayed correctly
    expect(screen.getByText('admin.deliveryZoneTypes.pickUp')).toBeInTheDocument();
    expect(screen.getByText('admin.deliveryZoneTypes.delivery')).toBeInTheDocument();
    expect(screen.getByText('admin.deliveryZoneTypes.inHouseTables')).toBeInTheDocument();
  });

  test('handles edit button click correctly', () => {
    render(<DeliveryZoneList {...mockProps} />);
    
    // Click the edit button for the first zone
    const editButtons = screen.getAllByText('admin.edit');
    fireEvent.click(editButtons[0]);
    
    // Check if onEdit was called with the correct zone
    expect(mockProps.onEdit).toHaveBeenCalledWith(mockZones[0]);
  });

  test('handles delete button click and confirmation correctly', async () => {
    render(<DeliveryZoneList {...mockProps} />);
    
    // Click the delete button for the first zone
    const deleteButtons = screen.getAllByText('admin.delete');
    fireEvent.click(deleteButtons[0]);
    
    // Check if the confirmation dialog is shown
    expect(screen.getByText('admin.confirmDelete')).toBeInTheDocument();
    
    // Click the confirm delete button
    const confirmDeleteButton = screen.getByText('common.deleteButton');
    fireEvent.click(confirmDeleteButton);
    
    // Check if deleteDeliveryZone was called with the correct zone ID
    await waitFor(() => {
      expect(firestoreModule.deleteDeliveryZone).toHaveBeenCalledWith('zone1');
      expect(mockProps.onDelete).toHaveBeenCalledWith('zone1');
    });
  });

  test('displays correct badge for active and inactive zones', () => {
    render(<DeliveryZoneList {...mockProps} />);
    
    // Check if active badges are displayed correctly
    const activeBadges = screen.getAllByText('admin.active');
    expect(activeBadges.length).toBe(2); // Two zones are active
    
    // Check if inactive badges are displayed correctly
    const inactiveBadges = screen.getAllByText('admin.inactive');
    expect(inactiveBadges.length).toBe(1); // One zone is inactive
  });
});
