import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DeliveryZoneForm from '../src/components/admin-dashboard/delivery-zones/DeliveryZoneForm';
import { DeliveryZone, DeliveryZoneType } from '@/types/models';
import * as firestoreModule from '@/lib/firebase/firestore';

// Mock the firestore module
jest.mock('@/lib/firebase/firestore', () => ({
  createDeliveryZone: jest.fn().mockImplementation((data) => Promise.resolve({ id: 'new-zone-id', ...data })),
  updateDeliveryZone: jest.fn().mockImplementation((id, data) => Promise.resolve({ id, ...data })),
}));

// Mock the locale context
jest.mock('@/contexts/LocaleContext', () => ({
  useLocale: () => ({
    t: (key: string) => key, // Return the key as the translation
    isClient: true,
  }),
}));

// Mock the auth context
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: { uid: 'test-user-id' },
  }),
}));

describe('DeliveryZoneForm Component', () => {
  const mockZone: DeliveryZone = {
    id: 'test-zone-id',
    userId: 'test-user-id',
    name: 'Test Zone',
    type: DeliveryZoneType.DELIVERY,
    isActive: true,
    deliveryFee: 5,
    minOrderAmount: 20,
    radius: 10,
    estimatedDeliveryTime: 30,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const mockProps = {
    editingZone: null,
    onZoneAdded: jest.fn(),
    onZoneUpdated: jest.fn(),
    onCancel: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders add zone form correctly', () => {
    render(<DeliveryZoneForm {...mockProps} />);
    
    // Check for form field labels using role and placeholder attributes
    expect(screen.getByLabelText(/admin.zoneName/i)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/admin.descriptionPlaceholder/i)).toBeInTheDocument();
    
    // Check for zone type radio buttons
    expect(screen.getByLabelText(/admin.pickUp/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/admin.delivery/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/admin.inHouseTables/i)).toBeInTheDocument();
    
    // Check if the form is in "add" mode
    expect(screen.getByText('admin.addZone')).toBeInTheDocument();
    expect(screen.queryByText('admin.updateZone')).not.toBeInTheDocument();
  });

  test('renders edit zone form correctly', () => {
    render(<DeliveryZoneForm {...mockProps} editingZone={mockZone} />);
    
    // Check if the form is in "edit" mode
    expect(screen.getByText('admin.updateZone')).toBeInTheDocument();
    expect(screen.queryByText('admin.addZone')).not.toBeInTheDocument();
    
    // Check if the form fields are populated with the zone data
    expect(screen.getByDisplayValue('Test Zone')).toBeInTheDocument();
    
    // Check if the delivery-specific fields are shown
    expect(screen.getByText('admin.deliverySettings')).toBeInTheDocument();
    expect(screen.getByDisplayValue('5')).toBeInTheDocument(); // deliveryFee
    expect(screen.getByDisplayValue('20')).toBeInTheDocument(); // minOrderAmount
    expect(screen.getByDisplayValue('10')).toBeInTheDocument(); // radius
    expect(screen.getByDisplayValue('30')).toBeInTheDocument(); // estimatedDeliveryTime
  });

  test('handles zone type change correctly', () => {
    render(<DeliveryZoneForm {...mockProps} />);
    
    // Initially, no type-specific fields should be shown
    expect(screen.queryByText('admin.deliverySettings')).not.toBeInTheDocument();
    expect(screen.queryByText('admin.tableSettings')).not.toBeInTheDocument();
    
    // Change to delivery type
    fireEvent.click(screen.getByLabelText('admin.delivery'));
    expect(screen.getByText('admin.deliverySettings')).toBeInTheDocument();
    
    // Change to in-house tables type
    fireEvent.click(screen.getByLabelText('admin.inHouseTables'));
    expect(screen.getByText('admin.tableSettings')).toBeInTheDocument();
  });

  test('handles table number addition and removal correctly', () => {
    render(<DeliveryZoneForm {...mockProps} />);
    
    // Change to in-house tables type
    fireEvent.click(screen.getByLabelText('admin.inHouseTables'));
    
    // Add a table number
    const tableInput = screen.getByPlaceholderText('admin.tableNumberPlaceholder');
    fireEvent.change(tableInput, { target: { value: 'A1' } });
    fireEvent.click(screen.getByText('admin.addTable'));
    
    // Check if the table number is added
    expect(screen.getByText('A1')).toBeInTheDocument();
    
    // Add another table number
    fireEvent.change(tableInput, { target: { value: 'B2' } });
    fireEvent.click(screen.getByText('admin.addTable'));
    
    // Check if both table numbers are shown
    expect(screen.getByText('A1')).toBeInTheDocument();
    expect(screen.getByText('B2')).toBeInTheDocument();
    
    // Remove a table number
    const removeButtons = screen.getAllByRole('button', { name: '' }); // The X buttons don't have accessible names
    fireEvent.click(removeButtons[0]); // Remove the first table number
    
    // Check if the table number is removed
    expect(screen.queryByText('A1')).not.toBeInTheDocument();
    expect(screen.getByText('B2')).toBeInTheDocument();
  });

  test('submits form to create new zone correctly', async () => {
    render(<DeliveryZoneForm {...mockProps} />);
    
    // Fill in the form
    fireEvent.change(screen.getByLabelText('admin.zoneName *'), { target: { value: 'New Zone' } });
    fireEvent.change(screen.getByLabelText('admin.description'), { target: { value: 'Test description' } });
    
    // Submit the form
    fireEvent.click(screen.getByText('admin.addZone'));
    
    // Check if createDeliveryZone was called with the correct data
    await waitFor(() => {
      expect(firestoreModule.createDeliveryZone).toHaveBeenCalledWith(expect.objectContaining({
        name: 'New Zone',
        description: 'Test description',
        type: DeliveryZoneType.PICK_UP, // Default type
        isActive: true, // Default value
        userId: 'test-user-id',
      }));
      expect(mockProps.onZoneAdded).toHaveBeenCalled();
    });
  });

  test('submits form to update existing zone correctly', async () => {
    render(<DeliveryZoneForm {...mockProps} editingZone={mockZone} />);
    
    // Change some fields
    fireEvent.change(screen.getByLabelText('admin.zoneName *'), { target: { value: 'Updated Zone' } });
    
    // Submit the form
    fireEvent.click(screen.getByText('admin.updateZone'));
    
    // Check if updateDeliveryZone was called with the correct data
    await waitFor(() => {
      expect(firestoreModule.updateDeliveryZone).toHaveBeenCalledWith('test-zone-id', expect.objectContaining({
        name: 'Updated Zone',
        type: DeliveryZoneType.DELIVERY,
        isActive: true,
        deliveryFee: 5,
        minOrderAmount: 20,
        radius: 10,
        estimatedDeliveryTime: 30,
      }));
      expect(mockProps.onZoneUpdated).toHaveBeenCalled();
    });
  });

  test('handles cancel button click correctly', () => {
    render(<DeliveryZoneForm {...mockProps} editingZone={mockZone} />);
    
    // Click the cancel button
    fireEvent.click(screen.getByText('common.cancel'));
    
    // Check if onCancel was called
    expect(mockProps.onCancel).toHaveBeenCalled();
  });
});
