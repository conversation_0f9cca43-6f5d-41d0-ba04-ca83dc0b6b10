# Barcode Cafe - Digital Menu & Customer Portal

A modern, interactive digital menu and customer portal for Barcode Cafe built with Next.js, Tailwind CSS, and modern web technologies.

<p align="center">
  <img src="public/logo-with-text.svg" alt="Barcode Cafe Logo" width="400">
</p>

## Features

### General Features
- Responsive design for all devices
- Dark & Light mode support
- Full internationalization (English & Arabic)
- RTL/LTR layout support
- Beautiful UI with consistent design system
- Smooth transitions and animations

### Customer Portal
- User authentication system
- Personalized customer dashboard
- Order history tracking
- Gift card management
- Saved addresses
- Reviews management
- Account settings
- Loyalty program tracking

### Menu Features
- Interactive menu categories
- Search functionality
- Menu filtering options
- High-quality food imagery
- Detailed item descriptions
- Customization options

## Tech Stack

- **Framework**: [Next.js](https://nextjs.org/) with App Router
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **Icons**: [Font Awesome](https://fontawesome.com/)
- **Typography**: [Inter](https://fonts.google.com/specimen/Inter)
- **Authentication**: Custom auth implementation
- **State Management**: React Context API
- **Internationalization**: Custom i18n implementation

## Getting Started

### Prerequisites

- Node.js 18+ and npm

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/barcode-cafe.git
   cd barcode-cafe
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Run the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

## Project Structure

```
barcode-cafe/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── customer/           # Customer portal pages
│   │   ├── signin/             # Authentication pages
│   │   ├── signup/             # Registration pages
│   │   └── [other routes]      # Menu and other pages
│   ├── assets/                 # Source assets (SVGs, etc.)
│   ├── components/             # Reusable components
│   │   ├── customer-dashboard/ # Dashboard-specific components
│   │   └── ui/                 # Generic UI components
│   ├── contexts/               # React Context providers
│   │   ├── LocaleContext.tsx   # Language/locale context
│   │   └── ThemeContext.tsx    # Dark/light mode context
│   ├── lib/                    # Utility functions
│   ├── locales/                # Translation files
│   │   ├── en.json             # English translations
│   │   └── ar.json             # Arabic translations
│   └── types/                  # TypeScript type definitions
├── public/                     # Static assets
├── next.config.js              # Next.js configuration
└── tailwind.config.js          # Tailwind CSS configuration
```

## User Stories

The application is being developed based on the following epics and user stories:

### Epic 1: User Management
- **View User Profile**: As an admin, I want to view the user profile so that I can manage user information and settings.
- **Edit User Profile**: As a user, I want to edit my profile information so that I can keep my details up to date.

### Epic 2: Menu Management
- **Add New Menu Items**: As an admin, I want to add new menu items so that I can update the offerings available to customers.
- **Edit Existing Menu Items**: As an admin, I want to edit existing menu items so that I can update prices and descriptions as needed.
- **Categorize Menu Items**: As an admin, I want to categorize menu items so that customers can easily navigate through different types of food and drinks.

### Epic 3: Order Management
- **View Order History**: As a user, I want to view my order history so that I can keep track of my past purchases.
- **View Recent Orders**: As an admin, I want to view recent orders so that I can manage and fulfill customer requests efficiently.

### Epic 4: Delivery Management
- **Manage Delivery Zones**: As an admin, I want to set up and manage delivery zones so that I can define the areas where delivery is available.
- **Check Delivery Zone**: As a user, I want to know if my address is within the delivery zone so that I can place an order confidently.

### Epic 5: Inventory Management
- **Track Inventory Levels**: As an admin, I want to track inventory levels so that I can manage stock quantities effectively.
- **Low Stock Alerts**: As an admin, I want to receive alerts for low stock items so that I can reorder supplies in a timely manner.

### Epic 6: Offers and Discounts
- **Create Special Offers**: As an admin, I want to create and manage special offers and discounts so that I can attract more customers.
- **View Available Offers**: As a user, I want to see available offers and discounts so that I can take advantage of promotions when ordering.

### Epic 7: Reviews and Feedback
- **Write Reviews**: As a user, I want to write reviews for menu items so that I can share my experience with others.
- **Monitor Customer Reviews**: As an admin, I want to monitor and respond to customer reviews so that I can improve service and address any concerns.

### Epic 8: QR Code Generation
- **Generate QR Codes**: As an admin, I want to generate QR codes for menu items so that customers can easily access the menu via their mobile devices.
- **Scan QR Codes**: As a user, I want to scan QR codes to view the menu quickly and conveniently.

### Epic 9: Payment Settings
- **Configure Payment Methods**: As an admin, I want to configure payment methods so that customers can choose their preferred payment options.
- **Save Payment Information**: As a user, I want to save my payment information securely for faster checkout in future orders.

For more detailed information on the user stories, refer to the [user-stories.md](user-stories.md) file.

## Internationalization

The application supports multiple languages with a custom internationalization system:

- Easily switch between English and Arabic
- RTL layout support for Arabic
- Translation keys organized by feature
- Special handling for RTL-specific styles
- Client-side language detection and persistence

## Empty States

The application includes carefully designed empty states for various sections:

- Order history when no orders exist
- Gift cards when no cards are present
- Addresses when no addresses are saved
- Reviews when no reviews have been written
- Settings with clear instructions for users

## Authentication Flow

The application implements a complete authentication flow:

1. Sign up page with form validation
2. Sign in with email/password
3. Authentication persistence
4. Protected routes for authenticated users
5. Account management

## Dark Mode Support

All pages support both light and dark modes:

- System preference detection
- Manual toggle option
- Theme persistence
- Carefully designed color schemes for both modes

## Customization

You can customize colors, fonts and other design elements in the following files:
- `src/app/globals.css` - Global CSS variables and styles
- `tailwind.config.js` - Tailwind configuration

## License

This project is licensed under the MIT License.

## Acknowledgements

- Design inspired by modern cafe websites
- Images from various sources for demonstration purposes
